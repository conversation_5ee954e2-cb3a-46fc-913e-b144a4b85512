{"$schema": "./node_modules/nx/schemas/nx-schema.json", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/.eslintrc.json", "!{projectRoot}/eslint.config.js"], "sharedGlobals": ["{workspaceRoot}/.github/workflows/ci.yml"]}, "nxCloudId": "67c2315752e26209c26ce414", "plugins": [{"plugin": "@nx/js/typescript", "options": {"typecheck": {"targetName": "typecheck"}, "build": {"targetName": "build", "configName": "tsconfig.lib.json", "buildDepsName": "build-deps", "watchDepsName": "watch-deps"}}}, {"plugin": "@nx/eslint/plugin", "options": {"targetName": "eslint:lint"}}], "release": {"projects": ["vincent-sdk"], "projectsRelationship": "independent", "releaseTagPattern": "vincent/{projectName}/{version}", "changelog": {"automaticFromRef": true, "projectChangelogs": true}, "version": {"preVersionCommand": "pnpm dlx nx build vincent-sdk"}}, "targetDefaults": {"nx-release-publish": {"dependsOn": [{"projects": "self", "target": "publish-typedoc"}]}}}