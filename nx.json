{"$schema": "./node_modules/nx/schemas/nx-schema.json", "affected": {"defaultBase": "main"}, "tasksRunnerOptions": {"default": {"runner": "@nx/workspace/tasks-runners/default", "options": {"cacheableOperations": ["build", "test", "lint", "e2e"]}}}, "targetDefaults": {"prebuild": {"dependsOn": ["^prebuild"], "inputs": ["production", "^production"]}, "build": {"cache": false, "dependsOn": ["^build"], "inputs": ["production", "^production"]}, "@nx/esbuild:esbuild": {"cache": true, "dependsOn": ["^build"], "inputs": ["production", "^production"]}}, "workspaceLayout": {"appsDir": "apps", "libsDir": "packages"}, "generators": {"@nx/web:application": {"style": "css", "linter": "eslint", "unitTestRunner": "jest", "e2eTestRunner": "cypress"}, "@nx/web:library": {"style": "css", "linter": "eslint", "unitTestRunner": "jest"}, "@nx/react": {"application": {"style": "css", "linter": "eslint", "babel": true}, "component": {"style": "css"}, "library": {"style": "css", "linter": "eslint"}}, "@nx/next": {"application": {"style": "css", "linter": "eslint"}}}, "defaultProject": "lit-node-client", "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "sharedGlobals": [], "production": ["default", "!{projectRoot}/src/test-setup.[jt]s"]}}