import { LitContracts } from '@lit-protocol/contracts-sdk';
import {
  LIT_EVM_CHAINS,
  LIT_NETWORK as POSSIBLE_LIT_NETWORKS,
} from '@lit-protocol/constants';
import {
  Action,
  type Address,
  type BlockData,
  EVMBlockListener,
  PKPInfo,
  StateMachine,
  type StateMachineDefinition,
} from '@lit-protocol/event-listener';
import { logger, setLoggerOptions } from '@lit-protocol/logger';
import { LitNodeClient } from '@lit-protocol/lit-node-client';

setLoggerOptions({
  transport: {
    target: 'pino-pretty',
  },
});

// const SERVER_URL = 'http://localhost:8080';
const SERVER_URL = 'https://state-machine-runner.onrender.com';

// const pkp = {} as any;

// ================ DatilDev
const LIT_NETWORK = POSSIBLE_LIT_NETWORKS.NagaDev;
const pkp = {
  tokenId: '0xea7ad9ba3ac2ec1e132c909165e696df06cca008bd2c2c4f5440c721ae949d6b',
  publicKey:
    '0451cfeaa77623b0498c1faaef1c01d264a0aa0385dd53a4d2464c401cbf5a383f292258cd3b95fddfe6b4c56f7e848d0a249e31bb6d54f0d23fb62f3302a5bdc3',
  ethAddress: '******************************************',
} as PKPInfo;

// ================ DatilTest
// const LIT_NETWORK = POSSIBLE_LIT_NETWORKS.DatilTest;
// const pkp = {
//   tokenId: '0x1f7818e3e17ed9d35b1650ee18196ce7ef40bb66dece77df47d83fc73e8e0e87',
//   publicKey:
//     '04a3c015dc9b0c13de03a4787c25837f40b6df6c77b837b970fd827596cfe08a77b2dd4d849902a911482e5cb3d356c7aded3fe1ef3d64b9017265741e833af95d',
//   ethAddress: '******************************************',
// } as PKPInfo;
const capacityTokenId = '72170';

const ethPrivateKey = process.env['ETHEREUM_PRIVATE_KEY'];
if (!ethPrivateKey) {
  throw new Error('ethPrivateKey not defined');
}

function onMachineError(error: Error) {
  logger.error({ error });
}

async function runLocalMachine(
  machineDefinition: StateMachineDefinition,
  initialState: string,
  timeout = 30 * 1000
) {
  const { debug = true, litContracts, litNodeClient } = machineDefinition;

  const stateMachine = StateMachine.fromDefinition({
    debug,
    onError: onMachineError,
    litContracts: { debug, ...litContracts },
    litNodeClient: { debug, ...litNodeClient },
    ...machineDefinition,
  });

  await stateMachine.startMachine(initialState);

  setTimeout(async () => {
    await stateMachine.stopMachine();
  }, timeout);
}

async function runRemoteMachine(
  machineDefinition,
  initialState: string,
  timeout = 30 * 1000
) {
  const automationCreateResponse = await fetch(
    `${SERVER_URL}/automation/create`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(machineDefinition),
    }
  );
  if (!automationCreateResponse.ok) {
    throw new Error(
      `Failed to create automation: ${automationCreateResponse.status} ${automationCreateResponse.statusText}`
    );
  }

  const automationCreateResponseJson = await automationCreateResponse.json();
  logger.info({
    msg: 'Automation creation response',
    response: automationCreateResponseJson,
  });

  // Start the automation state machine
  const automationStartResponse = await fetch(
    `${SERVER_URL}/automation/start/${automationCreateResponseJson.id}/${initialState}`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    }
  );
  if (!automationStartResponse.ok) {
    throw new Error(
      `Failed to start automation: ${automationStartResponse.status} ${automationStartResponse.statusText}`
    );
  }
  const automationStartResponseJson = await automationStartResponse.json();
  logger.info({
    msg: 'Automation start response',
    response: automationStartResponseJson,
  });

  // Stop the automation state machine after 15 seconds
  setTimeout(async () => {
    const automationStopResponse = await fetch(
      `${SERVER_URL}/automation/stop/${automationCreateResponseJson.id}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      }
    );
    if (!automationStopResponse.ok) {
      throw new Error(
        `Failed to stop automation: ${automationStopResponse.status} ${automationStopResponse.statusText}`
      );
    }
    const automationStopResponseJson = await automationStopResponse.json();
    logger.info({
      msg: 'Automation stop response',
      response: automationStopResponseJson,
    });
  }, timeout);
}

const USDC_ABI = [
  'event Transfer(address indexed from, address indexed to, uint256 value)',
];
const USDC_ETH_SEPOLIA_ADDRESS =
  '******************************************' as Address;
const USDC_BASE_SEPOLIA_ADDRESS =
  '******************************************' as Address;

function logAndExit(error) {
  logger.error({ error });
  process.exit(1);
}

async function runLitActionAfter() {
  const stateMachine = StateMachine.fromDefinition({
    debug: true,
    privateKey: ethPrivateKey,
    litNodeClient: {
      litNetwork: LIT_NETWORK,
    },
    litContracts: {
      network: LIT_NETWORK,
    },
    states: [
      {
        key: 'setPKP',
        actions: [
          {
            key: 'usePkp',
            pkp,
          },
        ],
        transitions: [{ toState: 'waitAndRun' }],
      },
      {
        key: 'waitAndRun',
        transitions: [
          {
            toState: 'waitAndRun',
            timer: {
              until: 10,
            },
            // actions: [
            //   {
            //     type: 'litAction',
            //     code: `(async () => {
            //   if (magicNumber >= 42) {
            //       LitActions.setResponse({ response:"The number is greater than or equal to 42!" });
            //   } else {
            //       LitActions.setResponse({ response: "The number is less than 42!" });
            //   }
            // })();`,
            //     jsParams: {
            //       magicNumber: Math.floor(Math.random() * 100),
            //     },
            //   },
            // ],
          },
        ],
      },
    ],
  });

  await stateMachine.startMachine('setPKP');
}
// runLitActionAfter().catch(logAndExit);

async function runLitActionInterval() {
  const stateMachine = StateMachine.fromDefinition({
    debug: true,
    privateKey: ethPrivateKey,
    litNodeClient: {
      litNetwork: LIT_NETWORK,
    },
    litContracts: {
      network: LIT_NETWORK,
    },
    states: [
      {
        key: 'setPKP',
        actions: [
          {
            key: 'usePkp',
            mint: true,
          },
        ],
        transitions: [{ toState: 'setCapacityNFT' }],
      },
      {
        key: 'setCapacityNFT',
        actions: [
          {
            key: 'useCapacityNFT',
            mint: true,
            daysUntilUTCMidnightExpiration: 10,
            requestPerSecond: 1,
          },
        ],
        transitions: [{ toState: 'runLitAction' }],
      },
      {
        key: 'runLitAction',
        actions: [
          {
            key: 'litAction',
            code: `(async () => {
              if (magicNumber >= 42) {
                  LitActions.setResponse({ response:"The number is greater than or equal to 42!" });
              } else {
                  LitActions.setResponse({ response: "The number is less than 42!" });
              }
            })();`,
            jsParams: {
              magicNumber: Math.floor(Math.random() * 100),
            },
          },
        ],
        transitions: [{ toState: 'cooldown' }],
      },
      {
        key: 'cooldown',
        transitions: [
          {
            toState: 'runLitAction',
            timer: {
              until: 10,
            },
          },
        ],
      },
    ],
  });

  await stateMachine.startMachine('setPKP');
}
// runLitActionInterval().catch(logAndExit);

async function polygonUSDCTransferMonitoring() {
  const debug = true;
  const stateMachine = StateMachine.fromDefinition({
    debug,
    actionRepository: {
      notify: class NotificationAction extends Action {
        constructor({
          stateMachine,
          notificationParam,
        }: {
          stateMachine: StateMachine;
          notificationParam: string;
        }) {
          super({
            debug,
            function: async () => {
              const context = stateMachine.getFromContext(
                'lastUSDCTransferAmount'
              );
              console.log('notificationParam', notificationParam);
              console.log('context', context);
            },
          });
        }
      },
    },
    privateKey: ethPrivateKey,
    litNodeClient: {
      litNetwork: LIT_NETWORK,
    },
    litContracts: {
      network: LIT_NETWORK,
    },
    states: [
      {
        key: 'monitorPolygonUSDC',
        transitions: [
          {
            evmContractEvent: {
              evmChainId: LIT_EVM_CHAINS.polygon.chainId,
              contractAddress:
                '******************************************' as const,
              contractABI: USDC_ABI,
              eventName: 'Transfer',
              contextUpdates: [
                {
                  contextPath: 'lastUSDCTransferAmount',
                  dataPath: 'event.args[2]',
                },
              ],
            },
            // toState: 'logContext',
            toState: 'monitorPolygonUSDC',
            actions: [
              {
                key: 'notify',
                notificationParam: 'longaniza',
              },
              {
                key: 'context',
                log: {
                  path: 'lastUSDCTransferAmount',
                },
              },
            ],
          },
        ],
      },
      // {
      //   key: 'logContext',
      //   actions: [
      //     {
      //       key: 'context',
      //       log: {
      //         path: 'lastUSDCTransferAmount',
      //       },
      //     },
      //   ],
      //   transitions: [{ toState: 'monitorPolygonUSDC' }],
      // },
    ],
  });

  await stateMachine.startMachine('monitorPolygonUSDC');
}
polygonUSDCTransferMonitoring().catch(logAndExit);

async function monitorEthereumBlocksWithHashEndingWithZero() {
  const litNodeClient = new LitNodeClient({
    litNetwork: LIT_NETWORK,
  });
  const litContracts = new LitContracts({
    network: LIT_NETWORK,
  });
  const stateMachine = new StateMachine({
    debug: true,
    // When the machine doesn't mint nor use Lit, these values do not matter
    privateKey: 'NOT_USED',
    litNodeClient,
    litContracts,
  });

  // Add each state individually
  stateMachine.addState({
    key: 'listenBlocks',
    onEnter: async () =>
      logger.info('Waiting for a block with a hash ending in 0'),
    onExit: async () => logger.info('Found a block whose hash ends in 0!'),
  });
  stateMachine.addState({
    key: 'autoAdvancingState',
  });

  // Then add transitions between states
  stateMachine.addTransition({
    // Because this transition does not have any listeners, it will be triggered automatically when the machine enters fromState
    fromState: 'autoAdvancingState',
    toState: 'listenBlocks',
  });
  stateMachine.addTransition({
    fromState: 'listenBlocks',
    toState: 'autoAdvancingState',
    // listeners are the ones that will produce the values that the transition will monitor
    listeners: [new EVMBlockListener(LIT_EVM_CHAINS.ethereum.rpcUrls[0])],
    // check is the function that will evaluate all values produced by listeners and define if there is a match or not
    check: async (values): Promise<boolean> => {
      // values are the results of all listeners
      const blockData = values[0] as BlockData;
      if (!blockData) return false;
      logger.info(`New block: ${blockData.number} (${blockData.hash})`);
      return blockData.hash.endsWith('0');
    },
    // when check finds a match (returns true) this function gets executed and the machine moves to toState
    onMatch: async (values) => {
      // values are the results of all listeners
      logger.info('We have matching values here');
    },
    onMismatch: undefined, // when check returns false (there is a mismatch) this function gets executed but the machine does not change state
    onError: undefined,
  });

  await stateMachine.startMachine('listenBlocks');
}
// monitorEthereumBlocksWithHashEndingWithZero().catch(logAndExit);

async function bridgeBaseSepoliaUSDCToEthereumSepolia() {
  const evmSourceNetwork = LIT_EVM_CHAINS.baseSepolia;
  const evmDestinationNetwork = LIT_EVM_CHAINS.sepolia;
  // const pkp = {
  //   tokenId: '0x123...',
  //   publicKey: '456...',
  //   ethAddress: '0x789...',
  // } as PKPInfo; // Minted Previously
  // const capacityTokenId = '123456'; // Minted previously
  // Because the pkp and the capacity token nft were minted previously, this private key only needs to be an authorized signer of the pkp. It can be empty, without funds of any kind
  // const ethPrivateKey = '0xTHE_PKP_AUTHORIZED_SIGNER_PRIVATE_KEY';

  const stateMachine = StateMachine.fromDefinition({
    debug: true,
    privateKey: ethPrivateKey, // Used only for authorization here, minting was done previously
    context: {
      // We can prepopulate the context, for example setting the pkp here instead of using state.usePkp later
      // activePkp: pkp,
      // transfer: ['******************************************', '1000'],
    },
    litNodeClient: {
      litNetwork: LIT_NETWORK,
    },
    litContracts: {
      network: LIT_NETWORK,
    },
    states: [
      {
        key: 'setPKP',
        actions: [
          {
            key: 'usePkp',
            pkp, // Configure the pkp passed. Not minting a new one
          },
        ],
        transitions: [{ toState: 'setCapacityNFT' }],
      },
      {
        key: 'setCapacityNFT',
        actions: [
          {
            key: 'useCapacityNFT',
            capacityTokenId: capacityTokenId, // Configure the capacity token to use. Not minting a new one
          },
        ],
        transitions: [{ toState: 'waitForFunds' }],
      },
      {
        key: 'waitForFunds',
        // Waits for our emitting PKP to have some USDC and native balance in destination chain
        transitions: [
          {
            toState: 'waitForTransfer',
            balances: [
              {
                address: pkp.ethAddress as Address,
                evmChainId: evmDestinationNetwork.chainId,
                type: 'native' as const,
                comparator: '>=' as const,
                amount: '0.001',
              },
              {
                address: pkp.ethAddress as Address,
                evmChainId: evmDestinationNetwork.chainId,
                type: 'ERC20' as const,
                tokenAddress: USDC_ETH_SEPOLIA_ADDRESS,
                tokenDecimals: 6,
                comparator: '>=' as const,
                amount: '20',
              },
            ],
          },
        ],
      },
      {
        key: 'waitForTransfer',
        actions: [
          {
            key: 'context',
            log: {
              path: '',
            },
          },
        ],
        transitions: [
          // Waits to receive an USDC transfer in our listening chain
          {
            toState: 'transferFunds',
            evmContractEvent: {
              evmChainId: evmSourceNetwork.chainId,
              contractAddress: USDC_BASE_SEPOLIA_ADDRESS,
              contractABI: USDC_ABI,
              eventName: 'Transfer',
              // Filter events using params for just listening the pkp.ethAddress as destination
              eventParams: [null, pkp.ethAddress],
              contextUpdates: [
                // The transition can perform some updates to the context
                {
                  contextPath: 'transfer.sender', // The context path to update
                  dataPath: 'event.args[0]', // The value from the event to save in the context
                },
                {
                  contextPath: 'transfer.amount',
                  dataPath: 'event.args[2]',
                },
              ],
            },
          },
        ],
      },
      {
        key: 'transferFunds',
        // Sends a transaction to transfer some USDC in destination chain
        actions: [
          {
            key: 'transaction',
            evmChainId: evmDestinationNetwork.chainId,
            contractAddress: USDC_ETH_SEPOLIA_ADDRESS,
            // data: '0xa9059cbb000000000000000000000000331fe6b8702738a86894a7f8fe7b28becf4ff528000000000000000000000000000000000000000000000000000000000000044c',
            contractABI: [
              'function transfer(address to, uint256 amount) public returns (bool)',
            ],
            method: 'transfer',
            // params: {
            //   contextPath: 'transfer',
            // },
            params: [
              // Params can be hardcoded values such as ['0x123...', '100'] or values from the state machine context
              {
                contextPath: 'transfer.sender',
              },
              {
                contextPath: 'transfer.amount',
              },
            ],
          },
        ],
        // Going back to waitForFunds to suspend machine if we need more sepolia eth or sepolia USDC
        transitions: [{ toState: 'waitForFunds' }],
      },
    ],
  });

  await stateMachine.startMachine('setPKP');
}
// bridgeBaseSepoliaUSDCToEthereumSepolia().catch(logAndExit);
