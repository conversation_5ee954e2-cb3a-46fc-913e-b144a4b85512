# See http://help.github.com/ignore-files/ for more about ignoring files.

# compiled output
/dist
/tmp
/out-tsc
**/dist
**/out-tsc

# dependencies
node_modules
/apps/e2e/node_modules

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# misc
/.sass-cache
/connect.lock
/coverage
/libpeerconnection.log
npm-debug.log
yarn-error.log
testem.log
/typings

# System Files
.DS_Store
Thumbs.db

.vscode

# Next.js
.next
lerna-debug.log

dev.sh
dev

doc

apps/demo-contracts-sdk-react/package-lock.json
apps/demo-encrypt-decrypt-react/package-lock.json
apps/demo-pkp-ethers-react/package-lock.json


dep-graph.json

apps/html/index.html
apps/nodejs/main.ts
apps/react/src/app/app.tsx

storage.test.db

.nx

.yalc

local-tests/build
# local-tests/setup/networkContext.json
.env

packages/wrapped-keys-lit-actions/src/generated
