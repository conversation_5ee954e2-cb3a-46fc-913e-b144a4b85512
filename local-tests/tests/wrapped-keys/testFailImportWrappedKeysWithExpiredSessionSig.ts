import { TinnyEnvironment } from 'local-tests/setup/tinny-environment';
import { api } from '@lit-protocol/wrapped-keys';
import { randomSolanaPrivateKey } from 'local-tests/setup/tinny-utils';
import { SessionSigsMap } from '@lit-protocol/types';

const { importPrivateKey } = api;

/**
 * Test Commands:
 * ✅ NETWORK=datil-dev yarn test:local --filter=testFailImportWrappedKeysWithExpiredSessionSig
 * ✅ NETWORK=datil-test yarn test:local --filter=testFailImportWrappedKeysWithExpiredSessionSig
 * ✅ NETWORK=custom yarn test:local --filter=testFailImportWrappedKeysWithExpiredSessionSig
 */
export const testFailImportWrappedKeysWithExpiredSessionSig = async (
  devEnv: TinnyEnvironment
) => {
  const pkpSessionSigs: SessionSigsMap = {
    'https://*************:8474': {
      sig: '1827d1c7b79c979ce76d0b9e130f6804dbf7c7838b6dfa41d4cadf690b9a8bec23321dde6cc573e8a592c395193074ade303d94f3c198d8f0017ca0aca91bd0f',
      derivedVia: 'litSessionSignViaNacl',
      signedMessage: `{"sessionKey":"4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"{\\"ProofOfPossession\\":\\"8f060f34f55e996e8396c5036cb456dbf3b3cf79a6c9d2a9c036a27dae6be5cb286c0170c45404ce60d45ad5df384a030450f4eabe61af68d7267d2de035a1ff0697097b3b32413581d8550b198599b8ee5c29a78999c05f8806e33923705748\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n0xd1Af1AAC50aC837C873200D17b78664aFCde597C\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. (3) 'Auth': 'Auth' for 'lit-resolvedauthcontext://*'.\\n\\nURI: lit:session:4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b\\nVersion: 1\\nChain ID: 1\\nNonce: 0xa8b687976835989b8ac57e8e6cb17fa316cc9ef74ea6174a588f08b11571829c\\nIssued At: 2024-06-02T19:46:47Z\\nExpiration Time: 2024-06-03T19:47:14.907Z\\nResources:\\n- urn:recap:***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","address":"0xd1Af1AAC50aC837C873200D17b78664aFCde597C"}],"issuedAt":"2024-06-02T19:47:16.707Z","expiration":"2024-06-03T19:47:14.907Z","nodeAddress":"https://*************:8474"}`,
      address:
        '4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b',
      algo: 'ed25519',
    },
    'https://*************:8473': {
      sig: '762b9849d2cc77d0c75aa354c3cce63abca008a9a07ec3efc69ee8a4954650c3362b8cb83cd3d63310ad98b446be5e68cb8193f9d486453b2df72188dc698d0e',
      derivedVia: 'litSessionSignViaNacl',
      signedMessage: `{"sessionKey":"4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"{\\"ProofOfPossession\\":\\"8f060f34f55e996e8396c5036cb456dbf3b3cf79a6c9d2a9c036a27dae6be5cb286c0170c45404ce60d45ad5df384a030450f4eabe61af68d7267d2de035a1ff0697097b3b32413581d8550b198599b8ee5c29a78999c05f8806e33923705748\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n0xd1Af1AAC50aC837C873200D17b78664aFCde597C\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. (3) 'Auth': 'Auth' for 'lit-resolvedauthcontext://*'.\\n\\nURI: lit:session:4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b\\nVersion: 1\\nChain ID: 1\\nNonce: 0xa8b687976835989b8ac57e8e6cb17fa316cc9ef74ea6174a588f08b11571829c\\nIssued At: 2024-06-02T19:46:47Z\\nExpiration Time: 2024-06-03T19:47:14.907Z\\nResources:\\n- urn:recap:***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","address":"0xd1Af1AAC50aC837C873200D17b78664aFCde597C"}],"issuedAt":"2024-06-02T19:47:16.707Z","expiration":"2024-06-03T19:47:14.907Z","nodeAddress":"https://*************:8473"}`,
      address:
        '4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b',
      algo: 'ed25519',
    },
    'https://*************:8475': {
      sig: '5e506dc973cc1540dcb3bd1de251afa687caf277cb5f3efe107339ecf4c25607d4bdf5d8c8910874519252e026a49cc66cea0b07bc5d38342c7cb2613decbe0a',
      derivedVia: 'litSessionSignViaNacl',
      signedMessage: `{"sessionKey":"4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"{\\"ProofOfPossession\\":\\"8f060f34f55e996e8396c5036cb456dbf3b3cf79a6c9d2a9c036a27dae6be5cb286c0170c45404ce60d45ad5df384a030450f4eabe61af68d7267d2de035a1ff0697097b3b32413581d8550b198599b8ee5c29a78999c05f8806e33923705748\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n0xd1Af1AAC50aC837C873200D17b78664aFCde597C\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. (3) 'Auth': 'Auth' for 'lit-resolvedauthcontext://*'.\\n\\nURI: lit:session:4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b\\nVersion: 1\\nChain ID: 1\\nNonce: 0xa8b687976835989b8ac57e8e6cb17fa316cc9ef74ea6174a588f08b11571829c\\nIssued At: 2024-06-02T19:46:47Z\\nExpiration Time: 2024-06-03T19:47:14.907Z\\nResources:\\n- urn:recap:***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************","address":"0xd1Af1AAC50aC837C873200D17b78664aFCde597C"}],"issuedAt":"2024-06-02T19:47:16.707Z","expiration":"2024-06-03T19:47:14.907Z","nodeAddress":"https://*************:8475"}`,
      address:
        '4fd3d6ae41190cdd33a07bc5feb4a51b0c882474e6b51eb37cf799d6668eb44b',
      algo: 'ed25519',
    },
  };

  try {
    const privateKey = randomSolanaPrivateKey();

    const res = await importPrivateKey({
      pkpSessionSigs,
      privateKey,
      litNodeClient: devEnv.litNodeClient,
      publicKey: '0xdeadbeef',
      keyType: 'K256',
      memo: 'Test key',
    });
    // console.log(res);
  } catch (e: any) {
    if (e.message.includes('Invalid sessionSig: Expired')) {
      console.log('✅ THIS IS EXPECTED: ', e);
      console.log(e.message);
      console.log(
        '✅ testFailImportWrappedKeysWithExpiredSessionSig is expected to have an error'
      );
    } else {
      throw e;
    }
  }

  console.log('✅ testFailImportWrappedKeysWithExpiredSessionSig');
};
