{"name": "@lit-protocol/js-sdk", "version": "0.0.1", "license": "MIT", "scripts": {"reset:hard": "yarn reset && sh ./tools/scripts/reset.sh", "reset:dev": "yarn tools --remove-local-dev", "reset": "rm -rf ./dist/packages && yarn reset:dev", "build": "yarn build:packages", "build:dev": "yarn tools --remove-local-dev && rm -rf ./dist && yarn tools check --no-empty-directories=true && yarn tools fixTsConfig && yarn tools --match-versions && yarn nx run-many --target=build && yarn tools --setup-local-dev && yarn build:verify", "build:packages": "yarn tools --remove-local-dev && rm -rf ./dist && yarn tools check --no-empty-directories=true && yarn tools fixTsConfig && yarn tools --match-versions && yarn nx run-many --target=build && yarn tools --setup-local-dev && yarn gen:readme && yarn build:verify && yarn nx format:write --all", "build:target": "yarn node tools/scripts/build.mjs", "build:setupLocalDev": "yarn tools --setup-local-dev", "build:verify": "yarn tools --verify", "bundles": "yarn node ./esbuilder/lit-connect-modal/esbuild.js", "postBuild:mapDistFolderNameToPackageJson": "node ./tools/scripts/map-dist-folder-name-to-package-json.mjs", "postBuild:mapDepsToDist": "node tools/scripts/map-deps-to-dist.mjs packages dist @lit-protocol", "test:ci": "nx affected --target=test --all --code-coverage", "test:local": "node ./local-tests/build.mjs && dotenvx run --env-file=.env -- node ./local-tests/build/test.mjs", "test:unit": "nx run-many --target=test", "test:unit:watch": "nx run-many --target=test --watch", "test:unit:bun": "bun ./tools/scripts/unit-test-with-bun.mjs", "publish:packages": "yarn node ./tools/scripts/pub.mjs --prod", "publish:beta": "yarn node ./tools/scripts/pub.mjs --tag beta", "publish:staging": "yarn node ./tools/scripts/pub.mjs --tag staging", "gen:docs": "node ./tools/scripts/gen-doc.mjs", "gen:readme": "yarn node ./tools/scripts/gen-readme.mjs", "update:contracts-sdk": "yarn node ./packages/contracts-sdk/tools.mjs", "tools": "yarn node ./tools/scripts/tools.mjs", "graph": "nx graph", "v": "node ./tools/scripts/get-npm-version.mjs", "prettier": "nx format:write --all", "prettier:check": "nx format:check --all"}, "private": true, "dependencies": {"@cosmjs/amino": "0.30.1", "@cosmjs/crypto": "0.30.1", "@cosmjs/encoding": "0.30.1", "@cosmjs/proto-signing": "0.30.1", "@cosmjs/stargate": "0.30.1", "@dotenvx/dotenvx": "^1.6.4", "@lit-protocol/accs-schemas": "^0.0.26", "@lit-protocol/contracts": "^0.0.74", "@metamask/eth-sig-util": "5.0.2", "@mysten/sui.js": "^0.37.1", "@openagenda/verror": "^3.1.4", "@simplewebauthn/browser": "^7.2.0", "@simplewebauthn/typescript-types": "^7.0.0", "@walletconnect/ethereum-provider": "2.9.2", "@walletconnect/jsonrpc-utils": "1.0.8", "@walletconnect/types": "2.9.2", "@walletconnect/utils": "2.9.2", "@walletconnect/web3wallet": "1.8.8", "ajv": "^8.12.0", "base64url": "^3.0.1", "bech32": "^2.0.0", "cbor-web": "^9.0.2", "cross-fetch": "3.1.8", "date-and-time": "^2.4.1", "depd": "^2.0.0", "ethers": "^5.7.1", "eventemitter3": "^5.0.1", "jose": "^4.14.4", "micromodal": "^0.4.10", "multiformats": "^9.7.1", "pako": "^2.1.0", "siwe": "^2.3.2", "siwe-recap": "0.0.2-alpha.0", "tslib": "^2.7.0", "tweetnacl": "^1.0.3", "tweetnacl-util": "^0.15.1", "typestub-ipfs-only-hash": "^4.0.0", "uint8arrays": "^4.0.3"}, "devDependencies": {"@nx/devkit": "17.3.0", "@nx/esbuild": "17.3.0", "@nx/eslint-plugin": "17.3.0", "@nx/jest": "17.3.0", "@nx/js": "17.3.0", "@nx/linter": "17.3.0", "@nx/next": "17.3.0", "@nx/node": "17.3.0", "@nx/plugin": "17.3.0", "@nx/react": "17.3.0", "@nx/web": "17.3.0", "@solana/web3.js": "1.95.3", "@types/depd": "^1.1.36", "@types/events": "^3.0.3", "@types/jest": "27.4.1", "@types/node": "18.19.18", "@types/secp256k1": "^4.0.6", "@typescript-eslint/eslint-plugin": "6.21.0", "@typescript-eslint/parser": "6.21.0", "axios": "^1.6.0", "babel-jest": "27.5.1", "buffer": "^6.0.3", "chalk": "^5.3.0", "cypress": "11.0.1", "cypress-metamask": "^1.0.5-development", "cypress-metamask-v2": "^1.7.2", "esbuild": "^0.17.3", "esbuild-node-builtins": "^0.1.0", "esbuild-node-externals": "^1.14.0", "esbuild-plugin-tsc": "^0.4.0", "eslint": "8.48.0", "eslint-config-next": "12.2.3", "eslint-config-prettier": "9.1.0", "eslint-import-resolver-typescript": "3.6.3", "eslint-plugin-import": "^2.29.1", "eslint-plugin-jsx-a11y": "6.9.0", "inquirer": "^9.2.21", "ipfs-only-hash": "^4.0.0", "ipfs-unixfs-importer": "12.0.1", "jest": "27.5.1", "lerna": "^5.4.3", "live-server": "^1.2.2", "node-fetch": "^2.6.1", "node-localstorage": "^3.0.5", "nx": "17.3.0", "path": "^0.12.7", "prettier": "^2.6.2", "ts-jest": "29.2.5", "typedoc": "^0.26.6", "typedoc-theme-hierarchy": "^5.0.0", "typescript": "5.5.4"}, "workspaces": ["packages/*"]}