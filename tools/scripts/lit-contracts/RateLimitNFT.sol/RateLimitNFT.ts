/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PayableOverrides,
  PopulatedTransaction,
  Signer,
  utils,
} from 'ethers';
import type {
  FunctionFragment,
  Result,
  EventFragment,
} from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
} from './common';

export declare namespace IDiamond {
  export type FacetCutStruct = {
    facetAddress: string;
    action: BigNumberish;
    functionSelectors: BytesLike[];
  };

  export type FacetCutStructOutput = [string, number, string[]] & {
    facetAddress: string;
    action: number;
    functionSelectors: string[];
  };
}

export declare namespace IDiamondLoupe {
  export type FacetStruct = {
    facetAddress: string;
    functionSelectors: BytesLike[];
  };

  export type FacetStructOutput = [string, string[]] & {
    facetAddress: string;
    functionSelectors: string[];
  };
}

export declare namespace LibRateLimitNFTStorage {
  export type RateLimitStruct = {
    requestsPerKilosecond: BigNumberish;
    expiresAt: BigNumberish;
  };

  export type RateLimitStructOutput = [BigNumber, BigNumber] & {
    requestsPerKilosecond: BigNumber;
    expiresAt: BigNumber;
  };
}

export interface RateLimitNFTInterface extends utils.Interface {
  functions: {
    'diamondCut((address,uint8,bytes4[])[],address,bytes)': FunctionFragment;
    'facetAddress(bytes4)': FunctionFragment;
    'facetAddresses()': FunctionFragment;
    'facetFunctionSelectors(address)': FunctionFragment;
    'facets()': FunctionFragment;
    'owner()': FunctionFragment;
    'transferOwnership(address)': FunctionFragment;
    'approve(address,uint256)': FunctionFragment;
    'balanceOf(address)': FunctionFragment;
    'burn(uint256)': FunctionFragment;
    'freeMint(uint256,uint256,bytes32,uint8,bytes32,bytes32)': FunctionFragment;
    'getApproved(uint256)': FunctionFragment;
    'initialize()': FunctionFragment;
    'isApprovedForAll(address,address)': FunctionFragment;
    'mint(uint256)': FunctionFragment;
    'name()': FunctionFragment;
    'ownerOf(uint256)': FunctionFragment;
    'safeTransferFrom(address,address,uint256)': FunctionFragment;
    'safeTransferFrom(address,address,uint256,bytes)': FunctionFragment;
    'setAdditionalRequestsPerKilosecondCost(uint256)': FunctionFragment;
    'setApprovalForAll(address,bool)': FunctionFragment;
    'setFreeMintSigner(address)': FunctionFragment;
    'setFreeRequestsPerRateLimitWindow(uint256)': FunctionFragment;
    'setMaxExpirationSeconds(uint256)': FunctionFragment;
    'setMaxRequestsPerKilosecond(uint256)': FunctionFragment;
    'setRLIHolderRateLimitWindowSeconds(uint256)': FunctionFragment;
    'setRateLimitWindowSeconds(uint256)': FunctionFragment;
    'supportsInterface(bytes4)': FunctionFragment;
    'symbol()': FunctionFragment;
    'tokenByIndex(uint256)': FunctionFragment;
    'tokenOfOwnerByIndex(address,uint256)': FunctionFragment;
    'tokenURI(uint256)': FunctionFragment;
    'totalSupply()': FunctionFragment;
    'transferFrom(address,address,uint256)': FunctionFragment;
    'withdraw()': FunctionFragment;
    'RLIHolderRateLimitWindowSeconds()': FunctionFragment;
    'additionalRequestsPerKilosecondCost()': FunctionFragment;
    'calculateCost(uint256,uint256)': FunctionFragment;
    'calculateRequestsPerKilosecond(uint256,uint256)': FunctionFragment;
    'capacity(uint256)': FunctionFragment;
    'checkBelowMaxRequestsPerKilosecond(uint256)': FunctionFragment;
    'currentSoldRequestsPerKilosecond()': FunctionFragment;
    'defaultRateLimitWindowSeconds()': FunctionFragment;
    'freeMintSigTest(uint256,uint256,bytes32,uint8,bytes32,bytes32)': FunctionFragment;
    'freeMintSigner()': FunctionFragment;
    'freeRequestsPerRateLimitWindow()': FunctionFragment;
    'isExpired(uint256)': FunctionFragment;
    'maxExpirationSeconds()': FunctionFragment;
    'maxRequestsPerKilosecond()': FunctionFragment;
    'prefixed(bytes32)': FunctionFragment;
    'redeemedFreeMints(bytes32)': FunctionFragment;
    'tokenIdCounter()': FunctionFragment;
    'tokenSVG(uint256)': FunctionFragment;
    'totalSoldRequestsPerKilosecondByExpirationTime(uint256)': FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | 'diamondCut'
      | 'facetAddress'
      | 'facetAddresses'
      | 'facetFunctionSelectors'
      | 'facets'
      | 'owner'
      | 'transferOwnership'
      | 'approve'
      | 'balanceOf'
      | 'burn'
      | 'freeMint'
      | 'getApproved'
      | 'initialize'
      | 'isApprovedForAll'
      | 'mint'
      | 'name'
      | 'ownerOf'
      | 'safeTransferFrom(address,address,uint256)'
      | 'safeTransferFrom(address,address,uint256,bytes)'
      | 'setAdditionalRequestsPerKilosecondCost'
      | 'setApprovalForAll'
      | 'setFreeMintSigner'
      | 'setFreeRequestsPerRateLimitWindow'
      | 'setMaxExpirationSeconds'
      | 'setMaxRequestsPerKilosecond'
      | 'setRLIHolderRateLimitWindowSeconds'
      | 'setRateLimitWindowSeconds'
      | 'supportsInterface'
      | 'symbol'
      | 'tokenByIndex'
      | 'tokenOfOwnerByIndex'
      | 'tokenURI'
      | 'totalSupply'
      | 'transferFrom'
      | 'withdraw'
      | 'RLIHolderRateLimitWindowSeconds'
      | 'additionalRequestsPerKilosecondCost'
      | 'calculateCost'
      | 'calculateRequestsPerKilosecond'
      | 'capacity'
      | 'checkBelowMaxRequestsPerKilosecond'
      | 'currentSoldRequestsPerKilosecond'
      | 'defaultRateLimitWindowSeconds'
      | 'freeMintSigTest'
      | 'freeMintSigner'
      | 'freeRequestsPerRateLimitWindow'
      | 'isExpired'
      | 'maxExpirationSeconds'
      | 'maxRequestsPerKilosecond'
      | 'prefixed'
      | 'redeemedFreeMints'
      | 'tokenIdCounter'
      | 'tokenSVG'
      | 'totalSoldRequestsPerKilosecondByExpirationTime'
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: 'diamondCut',
    values: [IDiamond.FacetCutStruct[], string, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'facetAddress',
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'facetAddresses',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'facetFunctionSelectors',
    values: [string]
  ): string;
  encodeFunctionData(functionFragment: 'facets', values?: undefined): string;
  encodeFunctionData(functionFragment: 'owner', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'transferOwnership',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'approve',
    values: [string, BigNumberish]
  ): string;
  encodeFunctionData(functionFragment: 'balanceOf', values: [string]): string;
  encodeFunctionData(functionFragment: 'burn', values: [BigNumberish]): string;
  encodeFunctionData(
    functionFragment: 'freeMint',
    values: [
      BigNumberish,
      BigNumberish,
      BytesLike,
      BigNumberish,
      BytesLike,
      BytesLike
    ]
  ): string;
  encodeFunctionData(
    functionFragment: 'getApproved',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'initialize',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'isApprovedForAll',
    values: [string, string]
  ): string;
  encodeFunctionData(functionFragment: 'mint', values: [BigNumberish]): string;
  encodeFunctionData(functionFragment: 'name', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'ownerOf',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'safeTransferFrom(address,address,uint256)',
    values: [string, string, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'safeTransferFrom(address,address,uint256,bytes)',
    values: [string, string, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'setAdditionalRequestsPerKilosecondCost',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setApprovalForAll',
    values: [string, boolean]
  ): string;
  encodeFunctionData(
    functionFragment: 'setFreeMintSigner',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'setFreeRequestsPerRateLimitWindow',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setMaxExpirationSeconds',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setMaxRequestsPerKilosecond',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setRLIHolderRateLimitWindowSeconds',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setRateLimitWindowSeconds',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'supportsInterface',
    values: [BytesLike]
  ): string;
  encodeFunctionData(functionFragment: 'symbol', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'tokenByIndex',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'tokenOfOwnerByIndex',
    values: [string, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'tokenURI',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'totalSupply',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'transferFrom',
    values: [string, string, BigNumberish]
  ): string;
  encodeFunctionData(functionFragment: 'withdraw', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'RLIHolderRateLimitWindowSeconds',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'additionalRequestsPerKilosecondCost',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'calculateCost',
    values: [BigNumberish, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'calculateRequestsPerKilosecond',
    values: [BigNumberish, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'capacity',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'checkBelowMaxRequestsPerKilosecond',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'currentSoldRequestsPerKilosecond',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'defaultRateLimitWindowSeconds',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'freeMintSigTest',
    values: [
      BigNumberish,
      BigNumberish,
      BytesLike,
      BigNumberish,
      BytesLike,
      BytesLike
    ]
  ): string;
  encodeFunctionData(
    functionFragment: 'freeMintSigner',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'freeRequestsPerRateLimitWindow',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'isExpired',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'maxExpirationSeconds',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'maxRequestsPerKilosecond',
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: 'prefixed', values: [BytesLike]): string;
  encodeFunctionData(
    functionFragment: 'redeemedFreeMints',
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'tokenIdCounter',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'tokenSVG',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'totalSoldRequestsPerKilosecondByExpirationTime',
    values: [BigNumberish]
  ): string;

  decodeFunctionResult(functionFragment: 'diamondCut', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'facetAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'facetAddresses',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'facetFunctionSelectors',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'facets', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'owner', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'transferOwnership',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'approve', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'balanceOf', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'burn', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'freeMint', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'getApproved',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'initialize', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'isApprovedForAll',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'mint', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'name', data: BytesLike): Result;
  decodeFunctionResult(functionFragment: 'ownerOf', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'safeTransferFrom(address,address,uint256)',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'safeTransferFrom(address,address,uint256,bytes)',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setAdditionalRequestsPerKilosecondCost',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setApprovalForAll',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setFreeMintSigner',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setFreeRequestsPerRateLimitWindow',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setMaxExpirationSeconds',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setMaxRequestsPerKilosecond',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setRLIHolderRateLimitWindowSeconds',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setRateLimitWindowSeconds',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'supportsInterface',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'symbol', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'tokenByIndex',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'tokenOfOwnerByIndex',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'tokenURI', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'totalSupply',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'transferFrom',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'withdraw', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'RLIHolderRateLimitWindowSeconds',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'additionalRequestsPerKilosecondCost',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'calculateCost',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'calculateRequestsPerKilosecond',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'capacity', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'checkBelowMaxRequestsPerKilosecond',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'currentSoldRequestsPerKilosecond',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'defaultRateLimitWindowSeconds',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'freeMintSigTest',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'freeMintSigner',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'freeRequestsPerRateLimitWindow',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'isExpired', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'maxExpirationSeconds',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'maxRequestsPerKilosecond',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'prefixed', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'redeemedFreeMints',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'tokenIdCounter',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'tokenSVG', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'totalSoldRequestsPerKilosecondByExpirationTime',
    data: BytesLike
  ): Result;

  events: {
    'DiamondCut((address,uint8,bytes4[])[],address,bytes)': EventFragment;
    'OwnershipTransferred(address,address)': EventFragment;
    'AdditionalRequestsPerKilosecondCostSet(uint256)': EventFragment;
    'Approval(address,address,uint256)': EventFragment;
    'ApprovalForAll(address,address,bool)': EventFragment;
    'FreeMintSignerSet(address)': EventFragment;
    'FreeRequestsPerRateLimitWindowSet(uint256)': EventFragment;
    'Initialized(uint8)': EventFragment;
    'RLIHolderRateLimitWindowSecondsSet(uint256)': EventFragment;
    'RateLimitWindowSecondsSet(uint256)': EventFragment;
    'Transfer(address,address,uint256)': EventFragment;
    'Withdrew(uint256)': EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: 'DiamondCut'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'OwnershipTransferred'): EventFragment;
  getEvent(
    nameOrSignatureOrTopic: 'AdditionalRequestsPerKilosecondCostSet'
  ): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Approval'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'ApprovalForAll'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'FreeMintSignerSet'): EventFragment;
  getEvent(
    nameOrSignatureOrTopic: 'FreeRequestsPerRateLimitWindowSet'
  ): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Initialized'): EventFragment;
  getEvent(
    nameOrSignatureOrTopic: 'RLIHolderRateLimitWindowSecondsSet'
  ): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'RateLimitWindowSecondsSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Transfer'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Withdrew'): EventFragment;
}

export interface DiamondCutEventObject {
  _diamondCut: IDiamond.FacetCutStructOutput[];
  _init: string;
  _calldata: string;
}
export type DiamondCutEvent = TypedEvent<
  [IDiamond.FacetCutStructOutput[], string, string],
  DiamondCutEventObject
>;

export type DiamondCutEventFilter = TypedEventFilter<DiamondCutEvent>;

export interface OwnershipTransferredEventObject {
  previousOwner: string;
  newOwner: string;
}
export type OwnershipTransferredEvent = TypedEvent<
  [string, string],
  OwnershipTransferredEventObject
>;

export type OwnershipTransferredEventFilter =
  TypedEventFilter<OwnershipTransferredEvent>;

export interface AdditionalRequestsPerKilosecondCostSetEventObject {
  newAdditionalRequestsPerKilosecondCost: BigNumber;
}
export type AdditionalRequestsPerKilosecondCostSetEvent = TypedEvent<
  [BigNumber],
  AdditionalRequestsPerKilosecondCostSetEventObject
>;

export type AdditionalRequestsPerKilosecondCostSetEventFilter =
  TypedEventFilter<AdditionalRequestsPerKilosecondCostSetEvent>;

export interface ApprovalEventObject {
  owner: string;
  approved: string;
  tokenId: BigNumber;
}
export type ApprovalEvent = TypedEvent<
  [string, string, BigNumber],
  ApprovalEventObject
>;

export type ApprovalEventFilter = TypedEventFilter<ApprovalEvent>;

export interface ApprovalForAllEventObject {
  owner: string;
  operator: string;
  approved: boolean;
}
export type ApprovalForAllEvent = TypedEvent<
  [string, string, boolean],
  ApprovalForAllEventObject
>;

export type ApprovalForAllEventFilter = TypedEventFilter<ApprovalForAllEvent>;

export interface FreeMintSignerSetEventObject {
  newFreeMintSigner: string;
}
export type FreeMintSignerSetEvent = TypedEvent<
  [string],
  FreeMintSignerSetEventObject
>;

export type FreeMintSignerSetEventFilter =
  TypedEventFilter<FreeMintSignerSetEvent>;

export interface FreeRequestsPerRateLimitWindowSetEventObject {
  newFreeRequestsPerRateLimitWindow: BigNumber;
}
export type FreeRequestsPerRateLimitWindowSetEvent = TypedEvent<
  [BigNumber],
  FreeRequestsPerRateLimitWindowSetEventObject
>;

export type FreeRequestsPerRateLimitWindowSetEventFilter =
  TypedEventFilter<FreeRequestsPerRateLimitWindowSetEvent>;

export interface InitializedEventObject {
  version: number;
}
export type InitializedEvent = TypedEvent<[number], InitializedEventObject>;

export type InitializedEventFilter = TypedEventFilter<InitializedEvent>;

export interface RLIHolderRateLimitWindowSecondsSetEventObject {
  newRLIHolderRateLimitWindowSeconds: BigNumber;
}
export type RLIHolderRateLimitWindowSecondsSetEvent = TypedEvent<
  [BigNumber],
  RLIHolderRateLimitWindowSecondsSetEventObject
>;

export type RLIHolderRateLimitWindowSecondsSetEventFilter =
  TypedEventFilter<RLIHolderRateLimitWindowSecondsSetEvent>;

export interface RateLimitWindowSecondsSetEventObject {
  newRateLimitWindowSeconds: BigNumber;
}
export type RateLimitWindowSecondsSetEvent = TypedEvent<
  [BigNumber],
  RateLimitWindowSecondsSetEventObject
>;

export type RateLimitWindowSecondsSetEventFilter =
  TypedEventFilter<RateLimitWindowSecondsSetEvent>;

export interface TransferEventObject {
  from: string;
  to: string;
  tokenId: BigNumber;
}
export type TransferEvent = TypedEvent<
  [string, string, BigNumber],
  TransferEventObject
>;

export type TransferEventFilter = TypedEventFilter<TransferEvent>;

export interface WithdrewEventObject {
  amount: BigNumber;
}
export type WithdrewEvent = TypedEvent<[BigNumber], WithdrewEventObject>;

export type WithdrewEventFilter = TypedEventFilter<WithdrewEvent>;

export interface RateLimitNFT extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: RateLimitNFTInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<[string] & { facetAddress_: string }>;

    facetAddresses(
      overrides?: CallOverrides
    ): Promise<[string[]] & { facetAddresses_: string[] }>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<[string[]] & { _facetFunctionSelectors: string[] }>;

    facets(overrides?: CallOverrides): Promise<
      [IDiamondLoupe.FacetStructOutput[]] & {
        facets_: IDiamondLoupe.FacetStructOutput[];
      }
    >;

    owner(overrides?: CallOverrides): Promise<[string] & { owner_: string }>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    approve(
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    balanceOf(owner: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    burn(
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    freeMint(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    getApproved(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string]>;

    initialize(
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    isApprovedForAll(
      owner: string,
      operator: string,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    mint(
      expiresAt: BigNumberish,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<ContractTransaction>;

    name(overrides?: CallOverrides): Promise<[string]>;

    ownerOf(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string]>;

    'safeTransferFrom(address,address,uint256)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    'safeTransferFrom(address,address,uint256,bytes)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setAdditionalRequestsPerKilosecondCost(
      newAdditionalRequestsPerKilosecondCost: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setApprovalForAll(
      operator: string,
      approved: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setFreeMintSigner(
      newFreeMintSigner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setFreeRequestsPerRateLimitWindow(
      newFreeRequestsPerRateLimitWindow: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setMaxExpirationSeconds(
      newMaxExpirationSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setMaxRequestsPerKilosecond(
      newMaxRequestsPerKilosecond: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setRLIHolderRateLimitWindowSeconds(
      newRLIHolderRateLimitWindowSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setRateLimitWindowSeconds(
      newRateLimitWindowSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    supportsInterface(
      interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    symbol(overrides?: CallOverrides): Promise<[string]>;

    tokenByIndex(
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    tokenOfOwnerByIndex(
      owner: string,
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    tokenURI(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string]>;

    totalSupply(overrides?: CallOverrides): Promise<[BigNumber]>;

    transferFrom(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    withdraw(
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    RLIHolderRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    additionalRequestsPerKilosecondCost(
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    calculateCost(
      requestsPerKilosecond: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    calculateRequestsPerKilosecond(
      payingAmount: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    capacity(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[LibRateLimitNFTStorage.RateLimitStructOutput]>;

    checkBelowMaxRequestsPerKilosecond(
      requestedRequestsPerKilosecond: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    currentSoldRequestsPerKilosecond(
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    defaultRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    freeMintSigTest(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: CallOverrides
    ): Promise<[void]>;

    freeMintSigner(overrides?: CallOverrides): Promise<[string]>;

    freeRequestsPerRateLimitWindow(
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    isExpired(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    maxExpirationSeconds(overrides?: CallOverrides): Promise<[BigNumber]>;

    maxRequestsPerKilosecond(overrides?: CallOverrides): Promise<[BigNumber]>;

    prefixed(hash: BytesLike, overrides?: CallOverrides): Promise<[string]>;

    redeemedFreeMints(
      msgHash: BytesLike,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    tokenIdCounter(overrides?: CallOverrides): Promise<[BigNumber]>;

    tokenSVG(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string]>;

    totalSoldRequestsPerKilosecondByExpirationTime(
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;
  };

  diamondCut(
    _diamondCut: IDiamond.FacetCutStruct[],
    _init: string,
    _calldata: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  facetAddress(
    _functionSelector: BytesLike,
    overrides?: CallOverrides
  ): Promise<string>;

  facetAddresses(overrides?: CallOverrides): Promise<string[]>;

  facetFunctionSelectors(
    _facet: string,
    overrides?: CallOverrides
  ): Promise<string[]>;

  facets(overrides?: CallOverrides): Promise<IDiamondLoupe.FacetStructOutput[]>;

  owner(overrides?: CallOverrides): Promise<string>;

  transferOwnership(
    _newOwner: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  approve(
    to: string,
    tokenId: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  balanceOf(owner: string, overrides?: CallOverrides): Promise<BigNumber>;

  burn(
    tokenId: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  freeMint(
    expiresAt: BigNumberish,
    requestsPerKilosecond: BigNumberish,
    msgHash: BytesLike,
    v: BigNumberish,
    r: BytesLike,
    sVal: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  getApproved(
    tokenId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<string>;

  initialize(
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  isApprovedForAll(
    owner: string,
    operator: string,
    overrides?: CallOverrides
  ): Promise<boolean>;

  mint(
    expiresAt: BigNumberish,
    overrides?: PayableOverrides & { from?: string }
  ): Promise<ContractTransaction>;

  name(overrides?: CallOverrides): Promise<string>;

  ownerOf(tokenId: BigNumberish, overrides?: CallOverrides): Promise<string>;

  'safeTransferFrom(address,address,uint256)'(
    from: string,
    to: string,
    tokenId: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  'safeTransferFrom(address,address,uint256,bytes)'(
    from: string,
    to: string,
    tokenId: BigNumberish,
    data: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setAdditionalRequestsPerKilosecondCost(
    newAdditionalRequestsPerKilosecondCost: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setApprovalForAll(
    operator: string,
    approved: boolean,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setFreeMintSigner(
    newFreeMintSigner: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setFreeRequestsPerRateLimitWindow(
    newFreeRequestsPerRateLimitWindow: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setMaxExpirationSeconds(
    newMaxExpirationSeconds: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setMaxRequestsPerKilosecond(
    newMaxRequestsPerKilosecond: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setRLIHolderRateLimitWindowSeconds(
    newRLIHolderRateLimitWindowSeconds: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setRateLimitWindowSeconds(
    newRateLimitWindowSeconds: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  supportsInterface(
    interfaceId: BytesLike,
    overrides?: CallOverrides
  ): Promise<boolean>;

  symbol(overrides?: CallOverrides): Promise<string>;

  tokenByIndex(
    index: BigNumberish,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  tokenOfOwnerByIndex(
    owner: string,
    index: BigNumberish,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  tokenURI(tokenId: BigNumberish, overrides?: CallOverrides): Promise<string>;

  totalSupply(overrides?: CallOverrides): Promise<BigNumber>;

  transferFrom(
    from: string,
    to: string,
    tokenId: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  withdraw(
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  RLIHolderRateLimitWindowSeconds(
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  additionalRequestsPerKilosecondCost(
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  calculateCost(
    requestsPerKilosecond: BigNumberish,
    expiresAt: BigNumberish,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  calculateRequestsPerKilosecond(
    payingAmount: BigNumberish,
    expiresAt: BigNumberish,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  capacity(
    tokenId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<LibRateLimitNFTStorage.RateLimitStructOutput>;

  checkBelowMaxRequestsPerKilosecond(
    requestedRequestsPerKilosecond: BigNumberish,
    overrides?: CallOverrides
  ): Promise<boolean>;

  currentSoldRequestsPerKilosecond(
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  defaultRateLimitWindowSeconds(overrides?: CallOverrides): Promise<BigNumber>;

  freeMintSigTest(
    expiresAt: BigNumberish,
    requestsPerKilosecond: BigNumberish,
    msgHash: BytesLike,
    v: BigNumberish,
    r: BytesLike,
    sVal: BytesLike,
    overrides?: CallOverrides
  ): Promise<void>;

  freeMintSigner(overrides?: CallOverrides): Promise<string>;

  freeRequestsPerRateLimitWindow(overrides?: CallOverrides): Promise<BigNumber>;

  isExpired(tokenId: BigNumberish, overrides?: CallOverrides): Promise<boolean>;

  maxExpirationSeconds(overrides?: CallOverrides): Promise<BigNumber>;

  maxRequestsPerKilosecond(overrides?: CallOverrides): Promise<BigNumber>;

  prefixed(hash: BytesLike, overrides?: CallOverrides): Promise<string>;

  redeemedFreeMints(
    msgHash: BytesLike,
    overrides?: CallOverrides
  ): Promise<boolean>;

  tokenIdCounter(overrides?: CallOverrides): Promise<BigNumber>;

  tokenSVG(tokenId: BigNumberish, overrides?: CallOverrides): Promise<string>;

  totalSoldRequestsPerKilosecondByExpirationTime(
    expiresAt: BigNumberish,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  callStatic: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<string>;

    facetAddresses(overrides?: CallOverrides): Promise<string[]>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<string[]>;

    facets(
      overrides?: CallOverrides
    ): Promise<IDiamondLoupe.FacetStructOutput[]>;

    owner(overrides?: CallOverrides): Promise<string>;

    transferOwnership(
      _newOwner: string,
      overrides?: CallOverrides
    ): Promise<void>;

    approve(
      to: string,
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    balanceOf(owner: string, overrides?: CallOverrides): Promise<BigNumber>;

    burn(tokenId: BigNumberish, overrides?: CallOverrides): Promise<void>;

    freeMint(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getApproved(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<string>;

    initialize(overrides?: CallOverrides): Promise<void>;

    isApprovedForAll(
      owner: string,
      operator: string,
      overrides?: CallOverrides
    ): Promise<boolean>;

    mint(
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    name(overrides?: CallOverrides): Promise<string>;

    ownerOf(tokenId: BigNumberish, overrides?: CallOverrides): Promise<string>;

    'safeTransferFrom(address,address,uint256)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    'safeTransferFrom(address,address,uint256,bytes)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      data: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    setAdditionalRequestsPerKilosecondCost(
      newAdditionalRequestsPerKilosecondCost: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setApprovalForAll(
      operator: string,
      approved: boolean,
      overrides?: CallOverrides
    ): Promise<void>;

    setFreeMintSigner(
      newFreeMintSigner: string,
      overrides?: CallOverrides
    ): Promise<void>;

    setFreeRequestsPerRateLimitWindow(
      newFreeRequestsPerRateLimitWindow: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setMaxExpirationSeconds(
      newMaxExpirationSeconds: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setMaxRequestsPerKilosecond(
      newMaxRequestsPerKilosecond: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setRLIHolderRateLimitWindowSeconds(
      newRLIHolderRateLimitWindowSeconds: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setRateLimitWindowSeconds(
      newRateLimitWindowSeconds: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    supportsInterface(
      interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<boolean>;

    symbol(overrides?: CallOverrides): Promise<string>;

    tokenByIndex(
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    tokenOfOwnerByIndex(
      owner: string,
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    tokenURI(tokenId: BigNumberish, overrides?: CallOverrides): Promise<string>;

    totalSupply(overrides?: CallOverrides): Promise<BigNumber>;

    transferFrom(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    withdraw(overrides?: CallOverrides): Promise<void>;

    RLIHolderRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    additionalRequestsPerKilosecondCost(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    calculateCost(
      requestsPerKilosecond: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    calculateRequestsPerKilosecond(
      payingAmount: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    capacity(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<LibRateLimitNFTStorage.RateLimitStructOutput>;

    checkBelowMaxRequestsPerKilosecond(
      requestedRequestsPerKilosecond: BigNumberish,
      overrides?: CallOverrides
    ): Promise<boolean>;

    currentSoldRequestsPerKilosecond(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    defaultRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    freeMintSigTest(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    freeMintSigner(overrides?: CallOverrides): Promise<string>;

    freeRequestsPerRateLimitWindow(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    isExpired(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<boolean>;

    maxExpirationSeconds(overrides?: CallOverrides): Promise<BigNumber>;

    maxRequestsPerKilosecond(overrides?: CallOverrides): Promise<BigNumber>;

    prefixed(hash: BytesLike, overrides?: CallOverrides): Promise<string>;

    redeemedFreeMints(
      msgHash: BytesLike,
      overrides?: CallOverrides
    ): Promise<boolean>;

    tokenIdCounter(overrides?: CallOverrides): Promise<BigNumber>;

    tokenSVG(tokenId: BigNumberish, overrides?: CallOverrides): Promise<string>;

    totalSoldRequestsPerKilosecondByExpirationTime(
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  filters: {
    'DiamondCut((address,uint8,bytes4[])[],address,bytes)'(
      _diamondCut?: null,
      _init?: null,
      _calldata?: null
    ): DiamondCutEventFilter;
    DiamondCut(
      _diamondCut?: null,
      _init?: null,
      _calldata?: null
    ): DiamondCutEventFilter;

    'OwnershipTransferred(address,address)'(
      previousOwner?: string | null,
      newOwner?: string | null
    ): OwnershipTransferredEventFilter;
    OwnershipTransferred(
      previousOwner?: string | null,
      newOwner?: string | null
    ): OwnershipTransferredEventFilter;

    'AdditionalRequestsPerKilosecondCostSet(uint256)'(
      newAdditionalRequestsPerKilosecondCost?: null
    ): AdditionalRequestsPerKilosecondCostSetEventFilter;
    AdditionalRequestsPerKilosecondCostSet(
      newAdditionalRequestsPerKilosecondCost?: null
    ): AdditionalRequestsPerKilosecondCostSetEventFilter;

    'Approval(address,address,uint256)'(
      owner?: string | null,
      approved?: string | null,
      tokenId?: BigNumberish | null
    ): ApprovalEventFilter;
    Approval(
      owner?: string | null,
      approved?: string | null,
      tokenId?: BigNumberish | null
    ): ApprovalEventFilter;

    'ApprovalForAll(address,address,bool)'(
      owner?: string | null,
      operator?: string | null,
      approved?: null
    ): ApprovalForAllEventFilter;
    ApprovalForAll(
      owner?: string | null,
      operator?: string | null,
      approved?: null
    ): ApprovalForAllEventFilter;

    'FreeMintSignerSet(address)'(
      newFreeMintSigner?: string | null
    ): FreeMintSignerSetEventFilter;
    FreeMintSignerSet(
      newFreeMintSigner?: string | null
    ): FreeMintSignerSetEventFilter;

    'FreeRequestsPerRateLimitWindowSet(uint256)'(
      newFreeRequestsPerRateLimitWindow?: null
    ): FreeRequestsPerRateLimitWindowSetEventFilter;
    FreeRequestsPerRateLimitWindowSet(
      newFreeRequestsPerRateLimitWindow?: null
    ): FreeRequestsPerRateLimitWindowSetEventFilter;

    'Initialized(uint8)'(version?: null): InitializedEventFilter;
    Initialized(version?: null): InitializedEventFilter;

    'RLIHolderRateLimitWindowSecondsSet(uint256)'(
      newRLIHolderRateLimitWindowSeconds?: null
    ): RLIHolderRateLimitWindowSecondsSetEventFilter;
    RLIHolderRateLimitWindowSecondsSet(
      newRLIHolderRateLimitWindowSeconds?: null
    ): RLIHolderRateLimitWindowSecondsSetEventFilter;

    'RateLimitWindowSecondsSet(uint256)'(
      newRateLimitWindowSeconds?: null
    ): RateLimitWindowSecondsSetEventFilter;
    RateLimitWindowSecondsSet(
      newRateLimitWindowSeconds?: null
    ): RateLimitWindowSecondsSetEventFilter;

    'Transfer(address,address,uint256)'(
      from?: string | null,
      to?: string | null,
      tokenId?: BigNumberish | null
    ): TransferEventFilter;
    Transfer(
      from?: string | null,
      to?: string | null,
      tokenId?: BigNumberish | null
    ): TransferEventFilter;

    'Withdrew(uint256)'(amount?: null): WithdrewEventFilter;
    Withdrew(amount?: null): WithdrewEventFilter;
  };

  estimateGas: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    facetAddresses(overrides?: CallOverrides): Promise<BigNumber>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    facets(overrides?: CallOverrides): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<BigNumber>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    approve(
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    balanceOf(owner: string, overrides?: CallOverrides): Promise<BigNumber>;

    burn(
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    freeMint(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    getApproved(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    initialize(overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    isApprovedForAll(
      owner: string,
      operator: string,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    mint(
      expiresAt: BigNumberish,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<BigNumber>;

    name(overrides?: CallOverrides): Promise<BigNumber>;

    ownerOf(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    'safeTransferFrom(address,address,uint256)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    'safeTransferFrom(address,address,uint256,bytes)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setAdditionalRequestsPerKilosecondCost(
      newAdditionalRequestsPerKilosecondCost: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setApprovalForAll(
      operator: string,
      approved: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setFreeMintSigner(
      newFreeMintSigner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setFreeRequestsPerRateLimitWindow(
      newFreeRequestsPerRateLimitWindow: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setMaxExpirationSeconds(
      newMaxExpirationSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setMaxRequestsPerKilosecond(
      newMaxRequestsPerKilosecond: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setRLIHolderRateLimitWindowSeconds(
      newRLIHolderRateLimitWindowSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setRateLimitWindowSeconds(
      newRateLimitWindowSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    supportsInterface(
      interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    symbol(overrides?: CallOverrides): Promise<BigNumber>;

    tokenByIndex(
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    tokenOfOwnerByIndex(
      owner: string,
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    tokenURI(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    totalSupply(overrides?: CallOverrides): Promise<BigNumber>;

    transferFrom(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    withdraw(overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    RLIHolderRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    additionalRequestsPerKilosecondCost(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    calculateCost(
      requestsPerKilosecond: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    calculateRequestsPerKilosecond(
      payingAmount: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    capacity(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    checkBelowMaxRequestsPerKilosecond(
      requestedRequestsPerKilosecond: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    currentSoldRequestsPerKilosecond(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    defaultRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    freeMintSigTest(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    freeMintSigner(overrides?: CallOverrides): Promise<BigNumber>;

    freeRequestsPerRateLimitWindow(
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    isExpired(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    maxExpirationSeconds(overrides?: CallOverrides): Promise<BigNumber>;

    maxRequestsPerKilosecond(overrides?: CallOverrides): Promise<BigNumber>;

    prefixed(hash: BytesLike, overrides?: CallOverrides): Promise<BigNumber>;

    redeemedFreeMints(
      msgHash: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    tokenIdCounter(overrides?: CallOverrides): Promise<BigNumber>;

    tokenSVG(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    totalSoldRequestsPerKilosecondByExpirationTime(
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    facetAddresses(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    facets(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    owner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    approve(
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    balanceOf(
      owner: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    burn(
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    freeMint(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    getApproved(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    initialize(
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    isApprovedForAll(
      owner: string,
      operator: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    mint(
      expiresAt: BigNumberish,
      overrides?: PayableOverrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    name(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    ownerOf(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    'safeTransferFrom(address,address,uint256)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    'safeTransferFrom(address,address,uint256,bytes)'(
      from: string,
      to: string,
      tokenId: BigNumberish,
      data: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setAdditionalRequestsPerKilosecondCost(
      newAdditionalRequestsPerKilosecondCost: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setApprovalForAll(
      operator: string,
      approved: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setFreeMintSigner(
      newFreeMintSigner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setFreeRequestsPerRateLimitWindow(
      newFreeRequestsPerRateLimitWindow: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setMaxExpirationSeconds(
      newMaxExpirationSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setMaxRequestsPerKilosecond(
      newMaxRequestsPerKilosecond: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setRLIHolderRateLimitWindowSeconds(
      newRLIHolderRateLimitWindowSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setRateLimitWindowSeconds(
      newRateLimitWindowSeconds: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    supportsInterface(
      interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    symbol(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    tokenByIndex(
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    tokenOfOwnerByIndex(
      owner: string,
      index: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    tokenURI(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    totalSupply(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    transferFrom(
      from: string,
      to: string,
      tokenId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    withdraw(
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    RLIHolderRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    additionalRequestsPerKilosecondCost(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    calculateCost(
      requestsPerKilosecond: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    calculateRequestsPerKilosecond(
      payingAmount: BigNumberish,
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    capacity(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    checkBelowMaxRequestsPerKilosecond(
      requestedRequestsPerKilosecond: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    currentSoldRequestsPerKilosecond(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    defaultRateLimitWindowSeconds(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    freeMintSigTest(
      expiresAt: BigNumberish,
      requestsPerKilosecond: BigNumberish,
      msgHash: BytesLike,
      v: BigNumberish,
      r: BytesLike,
      sVal: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    freeMintSigner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    freeRequestsPerRateLimitWindow(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    isExpired(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    maxExpirationSeconds(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    maxRequestsPerKilosecond(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    prefixed(
      hash: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    redeemedFreeMints(
      msgHash: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    tokenIdCounter(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    tokenSVG(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    totalSoldRequestsPerKilosecondByExpirationTime(
      expiresAt: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
