/* 
  Reference:
  https://www.radix-ui.com/docs/primitives 
*/

@import '@radix-ui/colors/blackA.css';
@import '@radix-ui/colors/green.css';
@import '@radix-ui/colors/mauve.css';
@import '@radix-ui/colors/slate.css';
@import '@radix-ui/colors/violet.css';
@import '@radix-ui/colors/red.css';

/* 
// --------------------------------------
//          Base Configuration                                    
// -------------------------------------- 
*/
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');

@import url('https://fonts.googleapis.com/css2?family=Figtree:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');

/**
Usage:
<main className="app" data-lit-theme="purple">
  <Component {...pageProps} />
</main>
[data-lit-theme="purple"] .button
**/
[data-lit-theme='purple'] {
  --lit-background-color: #0a132d;
  --lit-text-color: #e3e7ef;
  --lit-main-color: #7f53ad; /** eg. border, filled button **/
  --lit-secondary-color: #1f1e44;
  --lit-success-color: #10b981;
  --lit-alert-color: #ca3b31; /** eg. uninstall button **/
  --lit-disabled-color: #535a6c; /** eg. inactive/unfocused button **/

  --lit-border-radius: 6px; /** eg. button, input **/
  --lit-border-radius-icon: 12px;
  --lit-border-radius-tag: 999px; /** eg. tag **/

  /* usage */
  --lit-border-color: var(--lit-main-color);
  --lit-button-filled-color: var(--lit-main-color);
}

body {
  font-family: 'Figtree', 'Space Grotesk', -apple-system, BlinkMacSystemFont,
    Segoe UI, Roboto, Oxygen, Ubuntu, Cantarell, Fira Sans, Droid Sans,
    Helvetica Neue, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.heading h1 {
  background: linear-gradient(
      20deg,
      rgb(255, 244, 207) 10%,
      var(--lit-main-color) 100%
    )
    text;
}
@media screen and (min-width: 768px) {
  .heading h1 {
    font-size: 64px;
    line-height: 72px;
  }
}

@media screen and (min-width: 640px) {
  .heading h1 {
    font-size: 48px;
    line-height: 56px;
  }
}

.lit-loading {
  color: white;
  display: flex;
  flex-direction: column;
  align-self: center;
  font-size: 12px;
}

.lit-loading svg {
  width: 24px;
  height: 24px;
  margin: auto;
  text-align: center;
}
.capitalize {
  text-transform: capitalize;
}
ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* 
// ------------------------------
//          Animations
// ------------------------------ 
*/
@keyframes loading {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
