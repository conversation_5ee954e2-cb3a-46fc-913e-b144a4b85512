{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "ES2020", "module": "ES2020", "lib": ["ES2020", "dom", "ES2021.String"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "paths": {"@lit-protocol/*": ["packages/*/src"]}}, "exclude": ["node_modules", "tmp"]}