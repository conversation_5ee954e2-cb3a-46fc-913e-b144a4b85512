{"name": "core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/core/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/core", "main": "packages/core/src/index.ts", "tsConfig": "packages/core/tsconfig.lib.json", "assets": ["packages/core/*.md"], "updateBuildableProjectDepsInPackageJson": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/core/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/core"], "options": {"jestConfig": "packages/core/jest.config.ts", "passWithNoTests": true}}}, "tags": []}