import { SiweMessage } from 'siwe';
import { blsSessionSigVerify } from './validate-bls-session-sig';

describe('BlsSessionSigVerify', () => {
  const authSig = {
    sig: '{"ProofOfPossession":"ae925162cecb2f572fa76b93372dbbaee0133e89987c33d3210e0d62ca2dd5bf080dbdabb0155e61e770be1a2a629861073acc58fbc16cb6b700088d2aff114c42337c6123c8d15eeee63b522ea7d9c8f44390d3cb7b26e8d4935a283fe72a5d"}',
    algo: 'LIT_BLS',
    derivedVia: 'lit.bls',
    signedMessage:
      'litprotocol.com wants you to sign in with your Ethereum account:\n' +
      '******************************************\n' +
      '\n' +
      "Lit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. (3) 'Auth': 'Auth' for 'lit-resolvedauthcontext://*'.\n" +
      '\n' +
      'URI: lit:session:efebafcc9063827a49dffdb11c36b2d64a33330631ac7f5825e2960946bcc8ff\n' +
      'Version: 1\n' +
      'Chain ID: 1\n' +
      'Nonce: 0x1f623ab8dfe6bbd3b3dc22c7a041deb697c14817bce471b1bd1d86a25d5a319c\n' +
      'Issued At: 2024-06-11T15:55:23Z\n' +
      'Expiration Time: 2024-06-12T15:55:47.655Z\n' +
      'Resources:\n' +
      '- urn:recap:************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    address: '******************************************',
  };

  let networkPubKey =
    'a43499a4b786da2dd28af9f209eb152ff6f646b34b68a02954967271e17fb4c511fd67b81e067f690c6f38acab70585d';

  it(`should verify valid bls signatrue`, async () => {
    expect(
      await blsSessionSigVerify(
        async (
          publicKey: String,
          message: Uint8Array,
          signature: Uint8Array
        ): Promise<void> => {
          expect(typeof publicKey).toBe('string');
          expect(typeof message).toBe('object');
          expect(typeof signature).toBe('object');
        },
        networkPubKey,
        authSig,
        new SiweMessage({
          domain: 'localhost',
          statement:
            'litprotocol.com wants you to sign in with your Ethereum account:\n' +
            '******************************************\n' +
            '\n' +
            "Lit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. I further authorize the stated URI to perform the following actions on my behalf: (1) 'Threshold': 'Execution' for 'lit-litaction://*'. (2) 'Threshold': 'Signing' for 'lit-pkp://*'. (3) 'Auth': 'Auth' for 'lit-resolvedauthcontext://*'.\n",
          address: authSig.address,
          uri: 'lit:session:efebafcc9063827a49dffdb11c36b2d64a33330631ac7f5825e2960946bcc8ff',
          version: '1',
          nonce:
            '0x1f623ab8dfe6bbd3b3dc22c7a041deb697c14817bce471b1bd1d86a25d5a319c',
          expirationTime: new Date(
            Date.now() + 1000 * 60 * 60 * 24 * 7
          ).toISOString(),
          notBefore: new Date(Date.now()).toISOString(),
          issuedAt: new Date().toISOString(),
        })
      )
    ).toBeUndefined();
  });
});
