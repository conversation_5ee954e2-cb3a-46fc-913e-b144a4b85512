# Quick Start

This package provides essential cryptographic operations for the Lit Protocol by re-exporting TweetNaCl.js (https://www.npmjs.com/package/nacl). It offers a lightweight implementation of the NaCl (pronounced "salt") cryptography library, enabling secure encryption, decryption, and digital signatures across the Lit Protocol ecosystem.

### node.js / browser

```
yarn add @lit-protocol/nacl
```
