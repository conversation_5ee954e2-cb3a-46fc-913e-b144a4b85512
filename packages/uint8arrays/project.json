{"name": "uint8arrays", "$schema": "../../node_modules/nx/schemas/project-schema.json", "implicitDependencies": ["!misc-browser"], "sourceRoot": "packages/uint8arrays/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/uint8arrays", "main": "packages/uint8arrays/src/index.ts", "tsConfig": "packages/uint8arrays/tsconfig.lib.json", "assets": ["packages/uint8arrays/*.md"], "updateBuildableProjectDepsInPackageJson": true}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/uint8arrays"], "options": {"jestConfig": "packages/uint8arrays/jest.config.ts", "passWithNoTests": true}}}, "tags": []}