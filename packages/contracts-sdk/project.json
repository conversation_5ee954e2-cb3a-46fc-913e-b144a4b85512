{"name": "contracts-sdk", "$schema": "../../node_modules/nx/schemas/project-schema.json", "implicitDependencies": ["!pkp-ethers"], "sourceRoot": "packages/contracts-sdk/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/contracts-sdk", "main": "packages/contracts-sdk/src/index.ts", "tsConfig": "packages/contracts-sdk/tsconfig.lib.json", "assets": ["packages/contracts-sdk/*.md"], "updateBuildableProjectDepsInPackageJson": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/contracts-sdk/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/contracts-sdk"], "options": {"jestConfig": "packages/contracts-sdk/jest.config.ts", "passWithNoTests": true}}, "testWatch": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/contracts-sdk"], "options": {"jestConfig": "packages/contracts-sdk/jest.config.ts", "passWithNoTests": true, "watch": true}}}, "tags": []}