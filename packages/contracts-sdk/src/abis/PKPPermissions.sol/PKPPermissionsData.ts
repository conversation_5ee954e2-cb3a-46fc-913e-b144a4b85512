export const PKPPermissionsData = {
  date: '2023-11-14T15:45:41Z',
  address: '0xD01c9C30f8F6fa443721629775e1CC7DD9c9e209',
  contractName: 'PKPPermissions',
  abi: [
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_selector',
          type: 'bytes4',
        },
      ],
      name: 'CannotAddFunctionToDiamondThatAlreadyExists',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'bytes4[]',
          name: '_selectors',
          type: 'bytes4[]',
        },
      ],
      name: 'CannotAddSelectorsToZeroAddress',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_selector',
          type: 'bytes4',
        },
      ],
      name: 'CannotRemoveFunctionThatDoesNotExist',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_selector',
          type: 'bytes4',
        },
      ],
      name: 'CannotRemoveImmutableFunction',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_selector',
          type: 'bytes4',
        },
      ],
      name: 'CannotReplaceFunctionThatDoesNotExists',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_selector',
          type: 'bytes4',
        },
      ],
      name: 'CannotReplaceFunctionWithTheSameFunctionFromTheSameFacet',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'bytes4[]',
          name: '_selectors',
          type: 'bytes4[]',
        },
      ],
      name: 'CannotReplaceFunctionsFromFacetWithZeroAddress',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_selector',
          type: 'bytes4',
        },
      ],
      name: 'CannotReplaceImmutableFunction',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'uint8',
          name: '_action',
          type: 'uint8',
        },
      ],
      name: 'IncorrectFacetCutAction',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '_initializationContractAddress',
          type: 'address',
        },
        {
          internalType: 'bytes',
          name: '_calldata',
          type: 'bytes',
        },
      ],
      name: 'InitializationFunctionReverted',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '_contractAddress',
          type: 'address',
        },
        {
          internalType: 'string',
          name: '_message',
          type: 'string',
        },
      ],
      name: 'NoBytecodeAtAddress',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '_facetAddress',
          type: 'address',
        },
      ],
      name: 'NoSelectorsProvidedForFacetForCut',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '_user',
          type: 'address',
        },
        {
          internalType: 'address',
          name: '_contractOwner',
          type: 'address',
        },
      ],
      name: 'NotContractOwner',
      type: 'error',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '_facetAddress',
          type: 'address',
        },
      ],
      name: 'RemoveFacetAddressMustBeZeroAddress',
      type: 'error',
    },
    {
      anonymous: false,
      inputs: [
        {
          components: [
            {
              internalType: 'address',
              name: 'facetAddress',
              type: 'address',
            },
            {
              internalType: 'enum IDiamond.FacetCutAction',
              name: 'action',
              type: 'uint8',
            },
            {
              internalType: 'bytes4[]',
              name: 'functionSelectors',
              type: 'bytes4[]',
            },
          ],
          indexed: false,
          internalType: 'struct IDiamond.FacetCut[]',
          name: '_diamondCut',
          type: 'tuple[]',
        },
        {
          indexed: false,
          internalType: 'address',
          name: '_init',
          type: 'address',
        },
        {
          indexed: false,
          internalType: 'bytes',
          name: '_calldata',
          type: 'bytes',
        },
      ],
      name: 'DiamondCut',
      type: 'event',
    },
    {
      inputs: [
        {
          components: [
            {
              internalType: 'address',
              name: 'facetAddress',
              type: 'address',
            },
            {
              internalType: 'enum IDiamond.FacetCutAction',
              name: 'action',
              type: 'uint8',
            },
            {
              internalType: 'bytes4[]',
              name: 'functionSelectors',
              type: 'bytes4[]',
            },
          ],
          internalType: 'struct IDiamond.FacetCut[]',
          name: '_diamondCut',
          type: 'tuple[]',
        },
        {
          internalType: 'address',
          name: '_init',
          type: 'address',
        },
        {
          internalType: 'bytes',
          name: '_calldata',
          type: 'bytes',
        },
      ],
      name: 'diamondCut',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_functionSelector',
          type: 'bytes4',
        },
      ],
      name: 'facetAddress',
      outputs: [
        {
          internalType: 'address',
          name: 'facetAddress_',
          type: 'address',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [],
      name: 'facetAddresses',
      outputs: [
        {
          internalType: 'address[]',
          name: 'facetAddresses_',
          type: 'address[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '_facet',
          type: 'address',
        },
      ],
      name: 'facetFunctionSelectors',
      outputs: [
        {
          internalType: 'bytes4[]',
          name: '_facetFunctionSelectors',
          type: 'bytes4[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [],
      name: 'facets',
      outputs: [
        {
          components: [
            {
              internalType: 'address',
              name: 'facetAddress',
              type: 'address',
            },
            {
              internalType: 'bytes4[]',
              name: 'functionSelectors',
              type: 'bytes4[]',
            },
          ],
          internalType: 'struct IDiamondLoupe.Facet[]',
          name: 'facets_',
          type: 'tuple[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'bytes4',
          name: '_interfaceId',
          type: 'bytes4',
        },
      ],
      name: 'supportsInterface',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'address',
          name: 'previousOwner',
          type: 'address',
        },
        {
          indexed: true,
          internalType: 'address',
          name: 'newOwner',
          type: 'address',
        },
      ],
      name: 'OwnershipTransferred',
      type: 'event',
    },
    {
      inputs: [],
      name: 'owner',
      outputs: [
        {
          internalType: 'address',
          name: 'owner_',
          type: 'address',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: '_newOwner',
          type: 'address',
        },
      ],
      name: 'transferOwnership',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [],
      name: 'CallerNotOwner',
      type: 'error',
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: false,
          internalType: 'address',
          name: 'newResolverAddress',
          type: 'address',
        },
      ],
      name: 'ContractResolverAddressSet',
      type: 'event',
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
        {
          indexed: false,
          internalType: 'bytes',
          name: 'userPubkey',
          type: 'bytes',
        },
      ],
      name: 'PermittedAuthMethodAdded',
      type: 'event',
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
      ],
      name: 'PermittedAuthMethodRemoved',
      type: 'event',
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'scopeId',
          type: 'uint256',
        },
      ],
      name: 'PermittedAuthMethodScopeAdded',
      type: 'event',
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
        {
          indexed: false,
          internalType: 'uint256',
          name: 'scopeId',
          type: 'uint256',
        },
      ],
      name: 'PermittedAuthMethodScopeRemoved',
      type: 'event',
    },
    {
      anonymous: false,
      inputs: [
        {
          indexed: true,
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          indexed: true,
          internalType: 'uint256',
          name: 'group',
          type: 'uint256',
        },
        {
          indexed: false,
          internalType: 'bytes32',
          name: 'root',
          type: 'bytes32',
        },
      ],
      name: 'RootHashUpdated',
      type: 'event',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'ipfsCID',
          type: 'bytes',
        },
        {
          internalType: 'uint256[]',
          name: 'scopes',
          type: 'uint256[]',
        },
      ],
      name: 'addPermittedAction',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'address',
          name: 'user',
          type: 'address',
        },
        {
          internalType: 'uint256[]',
          name: 'scopes',
          type: 'uint256[]',
        },
      ],
      name: 'addPermittedAddress',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          components: [
            {
              internalType: 'uint256',
              name: 'authMethodType',
              type: 'uint256',
            },
            {
              internalType: 'bytes',
              name: 'id',
              type: 'bytes',
            },
            {
              internalType: 'bytes',
              name: 'userPubkey',
              type: 'bytes',
            },
          ],
          internalType: 'struct LibPKPPermissionsStorage.AuthMethod',
          name: 'authMethod',
          type: 'tuple',
        },
        {
          internalType: 'uint256[]',
          name: 'scopes',
          type: 'uint256[]',
        },
      ],
      name: 'addPermittedAuthMethod',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
        {
          internalType: 'uint256',
          name: 'scopeId',
          type: 'uint256',
        },
      ],
      name: 'addPermittedAuthMethodScope',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256[]',
          name: 'permittedAuthMethodTypesToAdd',
          type: 'uint256[]',
        },
        {
          internalType: 'bytes[]',
          name: 'permittedAuthMethodIdsToAdd',
          type: 'bytes[]',
        },
        {
          internalType: 'bytes[]',
          name: 'permittedAuthMethodPubkeysToAdd',
          type: 'bytes[]',
        },
        {
          internalType: 'uint256[][]',
          name: 'permittedAuthMethodScopesToAdd',
          type: 'uint256[][]',
        },
        {
          internalType: 'uint256[]',
          name: 'permittedAuthMethodTypesToRemove',
          type: 'uint256[]',
        },
        {
          internalType: 'bytes[]',
          name: 'permittedAuthMethodIdsToRemove',
          type: 'bytes[]',
        },
      ],
      name: 'batchAddRemoveAuthMethods',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
      ],
      name: 'getAuthMethodId',
      outputs: [
        {
          internalType: 'uint256',
          name: '',
          type: 'uint256',
        },
      ],
      stateMutability: 'pure',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
      ],
      name: 'getEthAddress',
      outputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
      ],
      name: 'getPermittedActions',
      outputs: [
        {
          internalType: 'bytes[]',
          name: '',
          type: 'bytes[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
      ],
      name: 'getPermittedAddresses',
      outputs: [
        {
          internalType: 'address[]',
          name: '',
          type: 'address[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
        {
          internalType: 'uint256',
          name: 'maxScopeId',
          type: 'uint256',
        },
      ],
      name: 'getPermittedAuthMethodScopes',
      outputs: [
        {
          internalType: 'bool[]',
          name: '',
          type: 'bool[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
      ],
      name: 'getPermittedAuthMethods',
      outputs: [
        {
          components: [
            {
              internalType: 'uint256',
              name: 'authMethodType',
              type: 'uint256',
            },
            {
              internalType: 'bytes',
              name: 'id',
              type: 'bytes',
            },
            {
              internalType: 'bytes',
              name: 'userPubkey',
              type: 'bytes',
            },
          ],
          internalType: 'struct LibPKPPermissionsStorage.AuthMethod[]',
          name: '',
          type: 'tuple[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [],
      name: 'getPkpNftAddress',
      outputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
      ],
      name: 'getPubkey',
      outputs: [
        {
          internalType: 'bytes',
          name: '',
          type: 'bytes',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [],
      name: 'getRouterAddress',
      outputs: [
        {
          internalType: 'address',
          name: '',
          type: 'address',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
      ],
      name: 'getTokenIdsForAuthMethod',
      outputs: [
        {
          internalType: 'uint256[]',
          name: '',
          type: 'uint256[]',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
      ],
      name: 'getUserPubkeyForAuthMethod',
      outputs: [
        {
          internalType: 'bytes',
          name: '',
          type: 'bytes',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'ipfsCID',
          type: 'bytes',
        },
      ],
      name: 'isPermittedAction',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'address',
          name: 'user',
          type: 'address',
        },
      ],
      name: 'isPermittedAddress',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
      ],
      name: 'isPermittedAuthMethod',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
        {
          internalType: 'uint256',
          name: 'scopeId',
          type: 'uint256',
        },
      ],
      name: 'isPermittedAuthMethodScopePresent',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'ipfsCID',
          type: 'bytes',
        },
      ],
      name: 'removePermittedAction',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'address',
          name: 'user',
          type: 'address',
        },
      ],
      name: 'removePermittedAddress',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
      ],
      name: 'removePermittedAuthMethod',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'authMethodType',
          type: 'uint256',
        },
        {
          internalType: 'bytes',
          name: 'id',
          type: 'bytes',
        },
        {
          internalType: 'uint256',
          name: 'scopeId',
          type: 'uint256',
        },
      ],
      name: 'removePermittedAuthMethodScope',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'address',
          name: 'newResolverAddress',
          type: 'address',
        },
      ],
      name: 'setContractResolver',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'group',
          type: 'uint256',
        },
        {
          internalType: 'bytes32',
          name: 'root',
          type: 'bytes32',
        },
      ],
      name: 'setRootHash',
      outputs: [],
      stateMutability: 'nonpayable',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'group',
          type: 'uint256',
        },
        {
          internalType: 'bytes32[]',
          name: 'proof',
          type: 'bytes32[]',
        },
        {
          internalType: 'bytes32',
          name: 'leaf',
          type: 'bytes32',
        },
      ],
      name: 'verifyState',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
    {
      inputs: [
        {
          internalType: 'uint256',
          name: 'tokenId',
          type: 'uint256',
        },
        {
          internalType: 'uint256',
          name: 'group',
          type: 'uint256',
        },
        {
          internalType: 'bytes32[]',
          name: 'proof',
          type: 'bytes32[]',
        },
        {
          internalType: 'bool[]',
          name: 'proofFlags',
          type: 'bool[]',
        },
        {
          internalType: 'bytes32[]',
          name: 'leaves',
          type: 'bytes32[]',
        },
      ],
      name: 'verifyStates',
      outputs: [
        {
          internalType: 'bool',
          name: '',
          type: 'bool',
        },
      ],
      stateMutability: 'view',
      type: 'function',
    },
  ],
};
