/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from 'ethers';
import type {
  FunctionFragment,
  Result,
  EventFragment,
} from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
} from './common';

export declare namespace IDiamond {
  export type FacetCutStruct = {
    facetAddress: string;
    action: BigNumberish;
    functionSelectors: BytesLike[];
  };

  export type FacetCutStructOutput = [string, number, string[]] & {
    facetAddress: string;
    action: number;
    functionSelectors: string[];
  };
}

export declare namespace IDiamondLoupe {
  export type FacetStruct = {
    facetAddress: string;
    functionSelectors: BytesLike[];
  };

  export type FacetStructOutput = [string, string[]] & {
    facetAddress: string;
    functionSelectors: string[];
  };
}

export declare namespace LibPKPPermissionsStorage {
  export type AuthMethodStruct = {
    authMethodType: BigNumberish;
    id: BytesLike;
    userPubkey: BytesLike;
  };

  export type AuthMethodStructOutput = [BigNumber, string, string] & {
    authMethodType: BigNumber;
    id: string;
    userPubkey: string;
  };
}

export interface PKPPermissionsInterface extends utils.Interface {
  functions: {
    'diamondCut((address,uint8,bytes4[])[],address,bytes)': FunctionFragment;
    'facetAddress(bytes4)': FunctionFragment;
    'facetAddresses()': FunctionFragment;
    'facetFunctionSelectors(address)': FunctionFragment;
    'facets()': FunctionFragment;
    'supportsInterface(bytes4)': FunctionFragment;
    'owner()': FunctionFragment;
    'transferOwnership(address)': FunctionFragment;
    'addPermittedAction(uint256,bytes,uint256[])': FunctionFragment;
    'addPermittedAddress(uint256,address,uint256[])': FunctionFragment;
    'addPermittedAuthMethod(uint256,(uint256,bytes,bytes),uint256[])': FunctionFragment;
    'addPermittedAuthMethodScope(uint256,uint256,bytes,uint256)': FunctionFragment;
    'batchAddRemoveAuthMethods(uint256,uint256[],bytes[],bytes[],uint256[][],uint256[],bytes[])': FunctionFragment;
    'getAuthMethodId(uint256,bytes)': FunctionFragment;
    'getEthAddress(uint256)': FunctionFragment;
    'getPermittedActions(uint256)': FunctionFragment;
    'getPermittedAddresses(uint256)': FunctionFragment;
    'getPermittedAuthMethodScopes(uint256,uint256,bytes,uint256)': FunctionFragment;
    'getPermittedAuthMethods(uint256)': FunctionFragment;
    'getPkpNftAddress()': FunctionFragment;
    'getPubkey(uint256)': FunctionFragment;
    'getRouterAddress()': FunctionFragment;
    'getTokenIdsForAuthMethod(uint256,bytes)': FunctionFragment;
    'getUserPubkeyForAuthMethod(uint256,bytes)': FunctionFragment;
    'isPermittedAction(uint256,bytes)': FunctionFragment;
    'isPermittedAddress(uint256,address)': FunctionFragment;
    'isPermittedAuthMethod(uint256,uint256,bytes)': FunctionFragment;
    'isPermittedAuthMethodScopePresent(uint256,uint256,bytes,uint256)': FunctionFragment;
    'removePermittedAction(uint256,bytes)': FunctionFragment;
    'removePermittedAddress(uint256,address)': FunctionFragment;
    'removePermittedAuthMethod(uint256,uint256,bytes)': FunctionFragment;
    'removePermittedAuthMethodScope(uint256,uint256,bytes,uint256)': FunctionFragment;
    'setContractResolver(address)': FunctionFragment;
    'setRootHash(uint256,uint256,bytes32)': FunctionFragment;
    'verifyState(uint256,uint256,bytes32[],bytes32)': FunctionFragment;
    'verifyStates(uint256,uint256,bytes32[],bool[],bytes32[])': FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | 'diamondCut'
      | 'facetAddress'
      | 'facetAddresses'
      | 'facetFunctionSelectors'
      | 'facets'
      | 'supportsInterface'
      | 'owner'
      | 'transferOwnership'
      | 'addPermittedAction'
      | 'addPermittedAddress'
      | 'addPermittedAuthMethod'
      | 'addPermittedAuthMethodScope'
      | 'batchAddRemoveAuthMethods'
      | 'getAuthMethodId'
      | 'getEthAddress'
      | 'getPermittedActions'
      | 'getPermittedAddresses'
      | 'getPermittedAuthMethodScopes'
      | 'getPermittedAuthMethods'
      | 'getPkpNftAddress'
      | 'getPubkey'
      | 'getRouterAddress'
      | 'getTokenIdsForAuthMethod'
      | 'getUserPubkeyForAuthMethod'
      | 'isPermittedAction'
      | 'isPermittedAddress'
      | 'isPermittedAuthMethod'
      | 'isPermittedAuthMethodScopePresent'
      | 'removePermittedAction'
      | 'removePermittedAddress'
      | 'removePermittedAuthMethod'
      | 'removePermittedAuthMethodScope'
      | 'setContractResolver'
      | 'setRootHash'
      | 'verifyState'
      | 'verifyStates'
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: 'diamondCut',
    values: [IDiamond.FacetCutStruct[], string, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'facetAddress',
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'facetAddresses',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'facetFunctionSelectors',
    values: [string]
  ): string;
  encodeFunctionData(functionFragment: 'facets', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'supportsInterface',
    values: [BytesLike]
  ): string;
  encodeFunctionData(functionFragment: 'owner', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'transferOwnership',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'addPermittedAction',
    values: [BigNumberish, BytesLike, BigNumberish[]]
  ): string;
  encodeFunctionData(
    functionFragment: 'addPermittedAddress',
    values: [BigNumberish, string, BigNumberish[]]
  ): string;
  encodeFunctionData(
    functionFragment: 'addPermittedAuthMethod',
    values: [
      BigNumberish,
      LibPKPPermissionsStorage.AuthMethodStruct,
      BigNumberish[]
    ]
  ): string;
  encodeFunctionData(
    functionFragment: 'addPermittedAuthMethodScope',
    values: [BigNumberish, BigNumberish, BytesLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'batchAddRemoveAuthMethods',
    values: [
      BigNumberish,
      BigNumberish[],
      BytesLike[],
      BytesLike[],
      BigNumberish[][],
      BigNumberish[],
      BytesLike[]
    ]
  ): string;
  encodeFunctionData(
    functionFragment: 'getAuthMethodId',
    values: [BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'getEthAddress',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'getPermittedActions',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'getPermittedAddresses',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'getPermittedAuthMethodScopes',
    values: [BigNumberish, BigNumberish, BytesLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'getPermittedAuthMethods',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'getPkpNftAddress',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'getPubkey',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'getRouterAddress',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'getTokenIdsForAuthMethod',
    values: [BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'getUserPubkeyForAuthMethod',
    values: [BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'isPermittedAction',
    values: [BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'isPermittedAddress',
    values: [BigNumberish, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'isPermittedAuthMethod',
    values: [BigNumberish, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'isPermittedAuthMethodScopePresent',
    values: [BigNumberish, BigNumberish, BytesLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'removePermittedAction',
    values: [BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'removePermittedAddress',
    values: [BigNumberish, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'removePermittedAuthMethod',
    values: [BigNumberish, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'removePermittedAuthMethodScope',
    values: [BigNumberish, BigNumberish, BytesLike, BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setContractResolver',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'setRootHash',
    values: [BigNumberish, BigNumberish, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'verifyState',
    values: [BigNumberish, BigNumberish, BytesLike[], BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'verifyStates',
    values: [BigNumberish, BigNumberish, BytesLike[], boolean[], BytesLike[]]
  ): string;

  decodeFunctionResult(functionFragment: 'diamondCut', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'facetAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'facetAddresses',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'facetFunctionSelectors',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'facets', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'supportsInterface',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'owner', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'transferOwnership',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'addPermittedAction',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'addPermittedAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'addPermittedAuthMethod',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'addPermittedAuthMethodScope',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'batchAddRemoveAuthMethods',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getAuthMethodId',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getEthAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getPermittedActions',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getPermittedAddresses',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getPermittedAuthMethodScopes',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getPermittedAuthMethods',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getPkpNftAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'getPubkey', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'getRouterAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getTokenIdsForAuthMethod',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getUserPubkeyForAuthMethod',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'isPermittedAction',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'isPermittedAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'isPermittedAuthMethod',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'isPermittedAuthMethodScopePresent',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'removePermittedAction',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'removePermittedAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'removePermittedAuthMethod',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'removePermittedAuthMethodScope',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setContractResolver',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setRootHash',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'verifyState',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'verifyStates',
    data: BytesLike
  ): Result;

  events: {
    'DiamondCut((address,uint8,bytes4[])[],address,bytes)': EventFragment;
    'OwnershipTransferred(address,address)': EventFragment;
    'ContractResolverAddressSet(address)': EventFragment;
    'PermittedAuthMethodAdded(uint256,uint256,bytes,bytes)': EventFragment;
    'PermittedAuthMethodRemoved(uint256,uint256,bytes)': EventFragment;
    'PermittedAuthMethodScopeAdded(uint256,uint256,bytes,uint256)': EventFragment;
    'PermittedAuthMethodScopeRemoved(uint256,uint256,bytes,uint256)': EventFragment;
    'RootHashUpdated(uint256,uint256,bytes32)': EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: 'DiamondCut'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'OwnershipTransferred'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'ContractResolverAddressSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'PermittedAuthMethodAdded'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'PermittedAuthMethodRemoved'): EventFragment;
  getEvent(
    nameOrSignatureOrTopic: 'PermittedAuthMethodScopeAdded'
  ): EventFragment;
  getEvent(
    nameOrSignatureOrTopic: 'PermittedAuthMethodScopeRemoved'
  ): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'RootHashUpdated'): EventFragment;
}

export interface DiamondCutEventObject {
  _diamondCut: IDiamond.FacetCutStructOutput[];
  _init: string;
  _calldata: string;
}
export type DiamondCutEvent = TypedEvent<
  [IDiamond.FacetCutStructOutput[], string, string],
  DiamondCutEventObject
>;

export type DiamondCutEventFilter = TypedEventFilter<DiamondCutEvent>;

export interface OwnershipTransferredEventObject {
  previousOwner: string;
  newOwner: string;
}
export type OwnershipTransferredEvent = TypedEvent<
  [string, string],
  OwnershipTransferredEventObject
>;

export type OwnershipTransferredEventFilter =
  TypedEventFilter<OwnershipTransferredEvent>;

export interface ContractResolverAddressSetEventObject {
  newResolverAddress: string;
}
export type ContractResolverAddressSetEvent = TypedEvent<
  [string],
  ContractResolverAddressSetEventObject
>;

export type ContractResolverAddressSetEventFilter =
  TypedEventFilter<ContractResolverAddressSetEvent>;

export interface PermittedAuthMethodAddedEventObject {
  tokenId: BigNumber;
  authMethodType: BigNumber;
  id: string;
  userPubkey: string;
}
export type PermittedAuthMethodAddedEvent = TypedEvent<
  [BigNumber, BigNumber, string, string],
  PermittedAuthMethodAddedEventObject
>;

export type PermittedAuthMethodAddedEventFilter =
  TypedEventFilter<PermittedAuthMethodAddedEvent>;

export interface PermittedAuthMethodRemovedEventObject {
  tokenId: BigNumber;
  authMethodType: BigNumber;
  id: string;
}
export type PermittedAuthMethodRemovedEvent = TypedEvent<
  [BigNumber, BigNumber, string],
  PermittedAuthMethodRemovedEventObject
>;

export type PermittedAuthMethodRemovedEventFilter =
  TypedEventFilter<PermittedAuthMethodRemovedEvent>;

export interface PermittedAuthMethodScopeAddedEventObject {
  tokenId: BigNumber;
  authMethodType: BigNumber;
  id: string;
  scopeId: BigNumber;
}
export type PermittedAuthMethodScopeAddedEvent = TypedEvent<
  [BigNumber, BigNumber, string, BigNumber],
  PermittedAuthMethodScopeAddedEventObject
>;

export type PermittedAuthMethodScopeAddedEventFilter =
  TypedEventFilter<PermittedAuthMethodScopeAddedEvent>;

export interface PermittedAuthMethodScopeRemovedEventObject {
  tokenId: BigNumber;
  authMethodType: BigNumber;
  id: string;
  scopeId: BigNumber;
}
export type PermittedAuthMethodScopeRemovedEvent = TypedEvent<
  [BigNumber, BigNumber, string, BigNumber],
  PermittedAuthMethodScopeRemovedEventObject
>;

export type PermittedAuthMethodScopeRemovedEventFilter =
  TypedEventFilter<PermittedAuthMethodScopeRemovedEvent>;

export interface RootHashUpdatedEventObject {
  tokenId: BigNumber;
  group: BigNumber;
  root: string;
}
export type RootHashUpdatedEvent = TypedEvent<
  [BigNumber, BigNumber, string],
  RootHashUpdatedEventObject
>;

export type RootHashUpdatedEventFilter = TypedEventFilter<RootHashUpdatedEvent>;

export interface PKPPermissions extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: PKPPermissionsInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<[string] & { facetAddress_: string }>;

    facetAddresses(
      overrides?: CallOverrides
    ): Promise<[string[]] & { facetAddresses_: string[] }>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<[string[]] & { _facetFunctionSelectors: string[] }>;

    facets(overrides?: CallOverrides): Promise<
      [IDiamondLoupe.FacetStructOutput[]] & {
        facets_: IDiamondLoupe.FacetStructOutput[];
      }
    >;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    owner(overrides?: CallOverrides): Promise<[string] & { owner_: string }>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    addPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    addPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    addPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethod: LibPKPPermissionsStorage.AuthMethodStruct,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    addPermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    batchAddRemoveAuthMethods(
      tokenId: BigNumberish,
      permittedAuthMethodTypesToAdd: BigNumberish[],
      permittedAuthMethodIdsToAdd: BytesLike[],
      permittedAuthMethodPubkeysToAdd: BytesLike[],
      permittedAuthMethodScopesToAdd: BigNumberish[][],
      permittedAuthMethodTypesToRemove: BigNumberish[],
      permittedAuthMethodIdsToRemove: BytesLike[],
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    getAuthMethodId(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<[BigNumber]>;

    getEthAddress(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string]>;

    getPermittedActions(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string[]]>;

    getPermittedAddresses(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string[]]>;

    getPermittedAuthMethodScopes(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      maxScopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[boolean[]]>;

    getPermittedAuthMethods(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[LibPKPPermissionsStorage.AuthMethodStructOutput[]]>;

    getPkpNftAddress(overrides?: CallOverrides): Promise<[string]>;

    getPubkey(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[string]>;

    getRouterAddress(overrides?: CallOverrides): Promise<[string]>;

    getTokenIdsForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<[BigNumber[]]>;

    getUserPubkeyForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<[string]>;

    isPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    isPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    isPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    isPermittedAuthMethodScopePresent(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    removePermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    removePermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    removePermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    removePermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setRootHash(
      tokenId: BigNumberish,
      group: BigNumberish,
      root: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    verifyState(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      leaf: BytesLike,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    verifyStates(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      proofFlags: boolean[],
      leaves: BytesLike[],
      overrides?: CallOverrides
    ): Promise<[boolean]>;
  };

  diamondCut(
    _diamondCut: IDiamond.FacetCutStruct[],
    _init: string,
    _calldata: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  facetAddress(
    _functionSelector: BytesLike,
    overrides?: CallOverrides
  ): Promise<string>;

  facetAddresses(overrides?: CallOverrides): Promise<string[]>;

  facetFunctionSelectors(
    _facet: string,
    overrides?: CallOverrides
  ): Promise<string[]>;

  facets(overrides?: CallOverrides): Promise<IDiamondLoupe.FacetStructOutput[]>;

  supportsInterface(
    _interfaceId: BytesLike,
    overrides?: CallOverrides
  ): Promise<boolean>;

  owner(overrides?: CallOverrides): Promise<string>;

  transferOwnership(
    _newOwner: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  addPermittedAction(
    tokenId: BigNumberish,
    ipfsCID: BytesLike,
    scopes: BigNumberish[],
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  addPermittedAddress(
    tokenId: BigNumberish,
    user: string,
    scopes: BigNumberish[],
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  addPermittedAuthMethod(
    tokenId: BigNumberish,
    authMethod: LibPKPPermissionsStorage.AuthMethodStruct,
    scopes: BigNumberish[],
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  addPermittedAuthMethodScope(
    tokenId: BigNumberish,
    authMethodType: BigNumberish,
    id: BytesLike,
    scopeId: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  batchAddRemoveAuthMethods(
    tokenId: BigNumberish,
    permittedAuthMethodTypesToAdd: BigNumberish[],
    permittedAuthMethodIdsToAdd: BytesLike[],
    permittedAuthMethodPubkeysToAdd: BytesLike[],
    permittedAuthMethodScopesToAdd: BigNumberish[][],
    permittedAuthMethodTypesToRemove: BigNumberish[],
    permittedAuthMethodIdsToRemove: BytesLike[],
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  getAuthMethodId(
    authMethodType: BigNumberish,
    id: BytesLike,
    overrides?: CallOverrides
  ): Promise<BigNumber>;

  getEthAddress(
    tokenId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<string>;

  getPermittedActions(
    tokenId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<string[]>;

  getPermittedAddresses(
    tokenId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<string[]>;

  getPermittedAuthMethodScopes(
    tokenId: BigNumberish,
    authMethodType: BigNumberish,
    id: BytesLike,
    maxScopeId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<boolean[]>;

  getPermittedAuthMethods(
    tokenId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<LibPKPPermissionsStorage.AuthMethodStructOutput[]>;

  getPkpNftAddress(overrides?: CallOverrides): Promise<string>;

  getPubkey(tokenId: BigNumberish, overrides?: CallOverrides): Promise<string>;

  getRouterAddress(overrides?: CallOverrides): Promise<string>;

  getTokenIdsForAuthMethod(
    authMethodType: BigNumberish,
    id: BytesLike,
    overrides?: CallOverrides
  ): Promise<BigNumber[]>;

  getUserPubkeyForAuthMethod(
    authMethodType: BigNumberish,
    id: BytesLike,
    overrides?: CallOverrides
  ): Promise<string>;

  isPermittedAction(
    tokenId: BigNumberish,
    ipfsCID: BytesLike,
    overrides?: CallOverrides
  ): Promise<boolean>;

  isPermittedAddress(
    tokenId: BigNumberish,
    user: string,
    overrides?: CallOverrides
  ): Promise<boolean>;

  isPermittedAuthMethod(
    tokenId: BigNumberish,
    authMethodType: BigNumberish,
    id: BytesLike,
    overrides?: CallOverrides
  ): Promise<boolean>;

  isPermittedAuthMethodScopePresent(
    tokenId: BigNumberish,
    authMethodType: BigNumberish,
    id: BytesLike,
    scopeId: BigNumberish,
    overrides?: CallOverrides
  ): Promise<boolean>;

  removePermittedAction(
    tokenId: BigNumberish,
    ipfsCID: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  removePermittedAddress(
    tokenId: BigNumberish,
    user: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  removePermittedAuthMethod(
    tokenId: BigNumberish,
    authMethodType: BigNumberish,
    id: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  removePermittedAuthMethodScope(
    tokenId: BigNumberish,
    authMethodType: BigNumberish,
    id: BytesLike,
    scopeId: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setContractResolver(
    newResolverAddress: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setRootHash(
    tokenId: BigNumberish,
    group: BigNumberish,
    root: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  verifyState(
    tokenId: BigNumberish,
    group: BigNumberish,
    proof: BytesLike[],
    leaf: BytesLike,
    overrides?: CallOverrides
  ): Promise<boolean>;

  verifyStates(
    tokenId: BigNumberish,
    group: BigNumberish,
    proof: BytesLike[],
    proofFlags: boolean[],
    leaves: BytesLike[],
    overrides?: CallOverrides
  ): Promise<boolean>;

  callStatic: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<string>;

    facetAddresses(overrides?: CallOverrides): Promise<string[]>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<string[]>;

    facets(
      overrides?: CallOverrides
    ): Promise<IDiamondLoupe.FacetStructOutput[]>;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<boolean>;

    owner(overrides?: CallOverrides): Promise<string>;

    transferOwnership(
      _newOwner: string,
      overrides?: CallOverrides
    ): Promise<void>;

    addPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      scopes: BigNumberish[],
      overrides?: CallOverrides
    ): Promise<void>;

    addPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      scopes: BigNumberish[],
      overrides?: CallOverrides
    ): Promise<void>;

    addPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethod: LibPKPPermissionsStorage.AuthMethodStruct,
      scopes: BigNumberish[],
      overrides?: CallOverrides
    ): Promise<void>;

    addPermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    batchAddRemoveAuthMethods(
      tokenId: BigNumberish,
      permittedAuthMethodTypesToAdd: BigNumberish[],
      permittedAuthMethodIdsToAdd: BytesLike[],
      permittedAuthMethodPubkeysToAdd: BytesLike[],
      permittedAuthMethodScopesToAdd: BigNumberish[][],
      permittedAuthMethodTypesToRemove: BigNumberish[],
      permittedAuthMethodIdsToRemove: BytesLike[],
      overrides?: CallOverrides
    ): Promise<void>;

    getAuthMethodId(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getEthAddress(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<string>;

    getPermittedActions(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<string[]>;

    getPermittedAddresses(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<string[]>;

    getPermittedAuthMethodScopes(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      maxScopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<boolean[]>;

    getPermittedAuthMethods(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<LibPKPPermissionsStorage.AuthMethodStructOutput[]>;

    getPkpNftAddress(overrides?: CallOverrides): Promise<string>;

    getPubkey(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<string>;

    getRouterAddress(overrides?: CallOverrides): Promise<string>;

    getTokenIdsForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber[]>;

    getUserPubkeyForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<string>;

    isPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: CallOverrides
    ): Promise<boolean>;

    isPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: CallOverrides
    ): Promise<boolean>;

    isPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<boolean>;

    isPermittedAuthMethodScopePresent(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<boolean>;

    removePermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    removePermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: CallOverrides
    ): Promise<void>;

    removePermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    removePermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: CallOverrides
    ): Promise<void>;

    setRootHash(
      tokenId: BigNumberish,
      group: BigNumberish,
      root: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    verifyState(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      leaf: BytesLike,
      overrides?: CallOverrides
    ): Promise<boolean>;

    verifyStates(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      proofFlags: boolean[],
      leaves: BytesLike[],
      overrides?: CallOverrides
    ): Promise<boolean>;
  };

  filters: {
    'DiamondCut((address,uint8,bytes4[])[],address,bytes)'(
      _diamondCut?: null,
      _init?: null,
      _calldata?: null
    ): DiamondCutEventFilter;
    DiamondCut(
      _diamondCut?: null,
      _init?: null,
      _calldata?: null
    ): DiamondCutEventFilter;

    'OwnershipTransferred(address,address)'(
      previousOwner?: string | null,
      newOwner?: string | null
    ): OwnershipTransferredEventFilter;
    OwnershipTransferred(
      previousOwner?: string | null,
      newOwner?: string | null
    ): OwnershipTransferredEventFilter;

    'ContractResolverAddressSet(address)'(
      newResolverAddress?: null
    ): ContractResolverAddressSetEventFilter;
    ContractResolverAddressSet(
      newResolverAddress?: null
    ): ContractResolverAddressSetEventFilter;

    'PermittedAuthMethodAdded(uint256,uint256,bytes,bytes)'(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null,
      userPubkey?: null
    ): PermittedAuthMethodAddedEventFilter;
    PermittedAuthMethodAdded(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null,
      userPubkey?: null
    ): PermittedAuthMethodAddedEventFilter;

    'PermittedAuthMethodRemoved(uint256,uint256,bytes)'(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null
    ): PermittedAuthMethodRemovedEventFilter;
    PermittedAuthMethodRemoved(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null
    ): PermittedAuthMethodRemovedEventFilter;

    'PermittedAuthMethodScopeAdded(uint256,uint256,bytes,uint256)'(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null,
      scopeId?: null
    ): PermittedAuthMethodScopeAddedEventFilter;
    PermittedAuthMethodScopeAdded(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null,
      scopeId?: null
    ): PermittedAuthMethodScopeAddedEventFilter;

    'PermittedAuthMethodScopeRemoved(uint256,uint256,bytes,uint256)'(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null,
      scopeId?: null
    ): PermittedAuthMethodScopeRemovedEventFilter;
    PermittedAuthMethodScopeRemoved(
      tokenId?: BigNumberish | null,
      authMethodType?: null,
      id?: null,
      scopeId?: null
    ): PermittedAuthMethodScopeRemovedEventFilter;

    'RootHashUpdated(uint256,uint256,bytes32)'(
      tokenId?: BigNumberish | null,
      group?: BigNumberish | null,
      root?: null
    ): RootHashUpdatedEventFilter;
    RootHashUpdated(
      tokenId?: BigNumberish | null,
      group?: BigNumberish | null,
      root?: null
    ): RootHashUpdatedEventFilter;
  };

  estimateGas: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    facetAddresses(overrides?: CallOverrides): Promise<BigNumber>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    facets(overrides?: CallOverrides): Promise<BigNumber>;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<BigNumber>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    addPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    addPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    addPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethod: LibPKPPermissionsStorage.AuthMethodStruct,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    addPermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    batchAddRemoveAuthMethods(
      tokenId: BigNumberish,
      permittedAuthMethodTypesToAdd: BigNumberish[],
      permittedAuthMethodIdsToAdd: BytesLike[],
      permittedAuthMethodPubkeysToAdd: BytesLike[],
      permittedAuthMethodScopesToAdd: BigNumberish[][],
      permittedAuthMethodTypesToRemove: BigNumberish[],
      permittedAuthMethodIdsToRemove: BytesLike[],
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    getAuthMethodId(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getEthAddress(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getPermittedActions(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getPermittedAddresses(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getPermittedAuthMethodScopes(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      maxScopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getPermittedAuthMethods(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getPkpNftAddress(overrides?: CallOverrides): Promise<BigNumber>;

    getPubkey(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getRouterAddress(overrides?: CallOverrides): Promise<BigNumber>;

    getTokenIdsForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getUserPubkeyForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    isPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    isPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    isPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    isPermittedAuthMethodScopePresent(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    removePermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    removePermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    removePermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    removePermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setRootHash(
      tokenId: BigNumberish,
      group: BigNumberish,
      root: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    verifyState(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      leaf: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    verifyStates(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      proofFlags: boolean[],
      leaves: BytesLike[],
      overrides?: CallOverrides
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    facetAddresses(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    facets(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    owner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    addPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    addPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    addPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethod: LibPKPPermissionsStorage.AuthMethodStruct,
      scopes: BigNumberish[],
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    addPermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    batchAddRemoveAuthMethods(
      tokenId: BigNumberish,
      permittedAuthMethodTypesToAdd: BigNumberish[],
      permittedAuthMethodIdsToAdd: BytesLike[],
      permittedAuthMethodPubkeysToAdd: BytesLike[],
      permittedAuthMethodScopesToAdd: BigNumberish[][],
      permittedAuthMethodTypesToRemove: BigNumberish[],
      permittedAuthMethodIdsToRemove: BytesLike[],
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    getAuthMethodId(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getEthAddress(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getPermittedActions(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getPermittedAddresses(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getPermittedAuthMethodScopes(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      maxScopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getPermittedAuthMethods(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getPkpNftAddress(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getPubkey(
      tokenId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getRouterAddress(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getTokenIdsForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getUserPubkeyForAuthMethod(
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    isPermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    isPermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    isPermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    isPermittedAuthMethodScopePresent(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    removePermittedAction(
      tokenId: BigNumberish,
      ipfsCID: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    removePermittedAddress(
      tokenId: BigNumberish,
      user: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    removePermittedAuthMethod(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    removePermittedAuthMethodScope(
      tokenId: BigNumberish,
      authMethodType: BigNumberish,
      id: BytesLike,
      scopeId: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setRootHash(
      tokenId: BigNumberish,
      group: BigNumberish,
      root: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    verifyState(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      leaf: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    verifyStates(
      tokenId: BigNumberish,
      group: BigNumberish,
      proof: BytesLike[],
      proofFlags: boolean[],
      leaves: BytesLike[],
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;
  };
}
