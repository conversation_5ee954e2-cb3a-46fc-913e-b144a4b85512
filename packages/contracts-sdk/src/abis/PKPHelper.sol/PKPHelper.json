{"date": "2023-11-14T15:45:41Z", "address": "0xF02b6D6b0970DB3810963300a6Ad38D8429c4cdb", "contractName": "PKPHelper", "abi": [{"inputs": [{"internalType": "address", "name": "_resolver", "type": "address"}, {"internalType": "enum ContractResolver.Env", "name": "_env", "type": "uint8"}], "stateMutability": "nonpayable", "type": "constructor"}, {"anonymous": false, "inputs": [{"indexed": false, "internalType": "address", "name": "newResolverAddress", "type": "address"}], "name": "ContractResolverAddressSet", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "address", "name": "previousOwner", "type": "address"}, {"indexed": true, "internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "OwnershipTransferred", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "previousAdminRole", "type": "bytes32"}, {"indexed": true, "internalType": "bytes32", "name": "newAdminRole", "type": "bytes32"}], "name": "RoleAdminChanged", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleGranted", "type": "event"}, {"anonymous": false, "inputs": [{"indexed": true, "internalType": "bytes32", "name": "role", "type": "bytes32"}, {"indexed": true, "internalType": "address", "name": "account", "type": "address"}, {"indexed": true, "internalType": "address", "name": "sender", "type": "address"}], "name": "RoleRevoked", "type": "event"}, {"inputs": [], "name": "DEFAULT_ADMIN_ROLE", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "keyType", "type": "uint256"}, {"internalType": "bytes32", "name": "derivedKeyId", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}, {"internalType": "uint8", "name": "v", "type": "uint8"}], "internalType": "struct IPubkeyRouter.Signature[]", "name": "signatures", "type": "tuple[]"}], "internalType": "struct LibPKPNFTStorage.ClaimMaterial", "name": "claimMaterial", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "keyType", "type": "uint256"}, {"internalType": "bytes[]", "name": "permittedIpfsCIDs", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedIpfsCIDScopes", "type": "uint256[][]"}, {"internalType": "address[]", "name": "permittedAddresses", "type": "address[]"}, {"internalType": "uint256[][]", "name": "permittedAddressScopes", "type": "uint256[][]"}, {"internalType": "uint256[]", "name": "permittedAuthMethodTypes", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodIds", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodPubkeys", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedAuthMethodScopes", "type": "uint256[][]"}, {"internalType": "bool", "name": "addPkpEthAddressAsPermittedAddress", "type": "bool"}, {"internalType": "bool", "name": "sendPkpToItself", "type": "bool"}], "internalType": "struct PKPHelper.AuthMethodData", "name": "authMethodData", "type": "tuple"}], "name": "claimAndMintNextAndAddAuthMethods", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"components": [{"internalType": "uint256", "name": "keyType", "type": "uint256"}, {"internalType": "bytes32", "name": "derivedKeyId", "type": "bytes32"}, {"components": [{"internalType": "bytes32", "name": "r", "type": "bytes32"}, {"internalType": "bytes32", "name": "s", "type": "bytes32"}, {"internalType": "uint8", "name": "v", "type": "uint8"}], "internalType": "struct IPubkeyRouter.Signature[]", "name": "signatures", "type": "tuple[]"}], "internalType": "struct LibPKPNFTStorage.ClaimMaterial", "name": "claimMaterial", "type": "tuple"}, {"components": [{"internalType": "uint256", "name": "keyType", "type": "uint256"}, {"internalType": "bytes[]", "name": "permittedIpfsCIDs", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedIpfsCIDScopes", "type": "uint256[][]"}, {"internalType": "address[]", "name": "permittedAddresses", "type": "address[]"}, {"internalType": "uint256[][]", "name": "permittedAddressScopes", "type": "uint256[][]"}, {"internalType": "uint256[]", "name": "permittedAuthMethodTypes", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodIds", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodPubkeys", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedAuthMethodScopes", "type": "uint256[][]"}, {"internalType": "bool", "name": "addPkpEthAddressAsPermittedAddress", "type": "bool"}, {"internalType": "bool", "name": "sendPkpToItself", "type": "bool"}], "internalType": "struct PKPHelper.AuthMethodData", "name": "authMethodData", "type": "tuple"}], "name": "claimAndMintNextAndAddAuthMethodsWithTypes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [], "name": "contractResolver", "outputs": [{"internalType": "contract ContractResolver", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "env", "outputs": [{"internalType": "enum ContractResolver.Env", "name": "", "type": "uint8"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getDomainWalletRegistry", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPKPNftMetdataAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPkpNftAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "getPkpPermissionsAddress", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}], "name": "getRoleAdmin", "outputs": [{"internalType": "bytes32", "name": "", "type": "bytes32"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "grantRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "hasRole", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "keyType", "type": "uint256"}, {"internalType": "uint256[]", "name": "permittedAuthMethodTypes", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodIds", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodPubkeys", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedAuthMethodScopes", "type": "uint256[][]"}, {"internalType": "bool", "name": "addPkpEthAddressAsPermittedAddress", "type": "bool"}, {"internalType": "bool", "name": "sendPkpToItself", "type": "bool"}], "name": "mintNextAndAddAuthMethods", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "keyType", "type": "uint256"}, {"internalType": "bytes[]", "name": "permittedIpfsCIDs", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedIpfsCIDScopes", "type": "uint256[][]"}, {"internalType": "address[]", "name": "permittedAddresses", "type": "address[]"}, {"internalType": "uint256[][]", "name": "permittedAddressScopes", "type": "uint256[][]"}, {"internalType": "uint256[]", "name": "permittedAuthMethodTypes", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodIds", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodPubkeys", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedAuthMethodScopes", "type": "uint256[][]"}, {"internalType": "bool", "name": "addPkpEthAddressAsPermittedAddress", "type": "bool"}, {"internalType": "bool", "name": "sendPkpToItself", "type": "bool"}], "name": "mintNextAndAddAuthMethodsWithTypes", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "keyType", "type": "uint256"}, {"internalType": "uint256[]", "name": "permittedAuthMethodTypes", "type": "uint256[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodIds", "type": "bytes[]"}, {"internalType": "bytes[]", "name": "permittedAuthMethodPubkeys", "type": "bytes[]"}, {"internalType": "uint256[][]", "name": "permittedAuthMethodScopes", "type": "uint256[][]"}, {"internalType": "string[]", "name": "nftMetadata", "type": "string[]"}, {"internalType": "bool", "name": "addPkpEthAddressAsPermittedAddress", "type": "bool"}, {"internalType": "bool", "name": "sendPkpToItself", "type": "bool"}], "name": "mintNextAndAddDomainWalletMetadata", "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}], "stateMutability": "payable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "", "type": "address"}, {"internalType": "address", "name": "", "type": "address"}, {"internalType": "uint256", "name": "", "type": "uint256"}, {"internalType": "bytes", "name": "", "type": "bytes"}], "name": "onERC721Received", "outputs": [{"internalType": "bytes4", "name": "", "type": "bytes4"}], "stateMutability": "view", "type": "function"}, {"inputs": [], "name": "owner", "outputs": [{"internalType": "address", "name": "", "type": "address"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}], "name": "removePkpMetadata", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [], "name": "renounceOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "renounceRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes32", "name": "role", "type": "bytes32"}, {"internalType": "address", "name": "account", "type": "address"}], "name": "revokeRole", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "address", "name": "newResolverAddress", "type": "address"}], "name": "setContractResolver", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "uint256", "name": "tokenId", "type": "uint256"}, {"internalType": "string[]", "name": "nftMetadata", "type": "string[]"}], "name": "setPkpMetadata", "outputs": [], "stateMutability": "nonpayable", "type": "function"}, {"inputs": [{"internalType": "bytes4", "name": "interfaceId", "type": "bytes4"}], "name": "supportsInterface", "outputs": [{"internalType": "bool", "name": "", "type": "bool"}], "stateMutability": "view", "type": "function"}, {"inputs": [{"internalType": "address", "name": "new<PERSON>wner", "type": "address"}], "name": "transferOwnership", "outputs": [], "stateMutability": "nonpayable", "type": "function"}]}