/* Autogenerated file. Do not edit manually. */
/* tslint:disable */
/* eslint-disable */
import type {
  BaseContract,
  BigNumber,
  BigNumberish,
  BytesLike,
  CallOverrides,
  ContractTransaction,
  Overrides,
  PopulatedTransaction,
  Signer,
  utils,
} from 'ethers';
import type {
  FunctionFragment,
  Result,
  EventFragment,
} from '@ethersproject/abi';
import type { Listener, Provider } from '@ethersproject/providers';
import type {
  TypedEventFilter,
  TypedEvent,
  TypedListener,
  OnEvent,
} from './common';

export declare namespace IDiamond {
  export type FacetCutStruct = {
    facetAddress: string;
    action: BigNumberish;
    functionSelectors: BytesLike[];
  };

  export type FacetCutStructOutput = [string, number, string[]] & {
    facetAddress: string;
    action: number;
    functionSelectors: string[];
  };
}

export declare namespace IDiamondLoupe {
  export type FacetStruct = {
    facetAddress: string;
    functionSelectors: BytesLike[];
  };

  export type FacetStructOutput = [string, string[]] & {
    facetAddress: string;
    functionSelectors: string[];
  };
}

export interface StakingBalancesInterface extends utils.Interface {
  functions: {
    'diamondCut((address,uint8,bytes4[])[],address,bytes)': FunctionFragment;
    'facetAddress(bytes4)': FunctionFragment;
    'facetAddresses()': FunctionFragment;
    'facetFunctionSelectors(address)': FunctionFragment;
    'facets()': FunctionFragment;
    'supportsInterface(bytes4)': FunctionFragment;
    'owner()': FunctionFragment;
    'transferOwnership(address)': FunctionFragment;
    'addAlias(address)': FunctionFragment;
    'addPermittedStaker(address)': FunctionFragment;
    'addPermittedStakers(address[])': FunctionFragment;
    'balanceOf(address)': FunctionFragment;
    'checkStakingAmounts(address)': FunctionFragment;
    'getReward(address)': FunctionFragment;
    'getStakingAddress()': FunctionFragment;
    'getTokenAddress()': FunctionFragment;
    'isPermittedStaker(address)': FunctionFragment;
    'maximumStake()': FunctionFragment;
    'minimumStake()': FunctionFragment;
    'penalizeTokens(uint256,address)': FunctionFragment;
    'permittedStakersOn()': FunctionFragment;
    'removeAlias(address)': FunctionFragment;
    'removePermittedStaker(address)': FunctionFragment;
    'restakePenaltyTokens(address,uint256)': FunctionFragment;
    'rewardOf(address)': FunctionFragment;
    'rewardValidator(uint256,address)': FunctionFragment;
    'setContractResolver(address)': FunctionFragment;
    'setMaxAliasCount(uint256)': FunctionFragment;
    'setMaximumStake(uint256)': FunctionFragment;
    'setMinimumStake(uint256)': FunctionFragment;
    'setPermittedStakersOn(bool)': FunctionFragment;
    'stake(uint256,address)': FunctionFragment;
    'totalStaked()': FunctionFragment;
    'transferPenaltyTokens(uint256,address)': FunctionFragment;
    'withdraw(uint256,address)': FunctionFragment;
    'withdraw()': FunctionFragment;
    'withdrawPenaltyTokens(uint256)': FunctionFragment;
  };

  getFunction(
    nameOrSignatureOrTopic:
      | 'diamondCut'
      | 'facetAddress'
      | 'facetAddresses'
      | 'facetFunctionSelectors'
      | 'facets'
      | 'supportsInterface'
      | 'owner'
      | 'transferOwnership'
      | 'addAlias'
      | 'addPermittedStaker'
      | 'addPermittedStakers'
      | 'balanceOf'
      | 'checkStakingAmounts'
      | 'getReward'
      | 'getStakingAddress'
      | 'getTokenAddress'
      | 'isPermittedStaker'
      | 'maximumStake'
      | 'minimumStake'
      | 'penalizeTokens'
      | 'permittedStakersOn'
      | 'removeAlias'
      | 'removePermittedStaker'
      | 'restakePenaltyTokens'
      | 'rewardOf'
      | 'rewardValidator'
      | 'setContractResolver'
      | 'setMaxAliasCount'
      | 'setMaximumStake'
      | 'setMinimumStake'
      | 'setPermittedStakersOn'
      | 'stake'
      | 'totalStaked'
      | 'transferPenaltyTokens'
      | 'withdraw(uint256,address)'
      | 'withdraw()'
      | 'withdrawPenaltyTokens'
  ): FunctionFragment;

  encodeFunctionData(
    functionFragment: 'diamondCut',
    values: [IDiamond.FacetCutStruct[], string, BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'facetAddress',
    values: [BytesLike]
  ): string;
  encodeFunctionData(
    functionFragment: 'facetAddresses',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'facetFunctionSelectors',
    values: [string]
  ): string;
  encodeFunctionData(functionFragment: 'facets', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'supportsInterface',
    values: [BytesLike]
  ): string;
  encodeFunctionData(functionFragment: 'owner', values?: undefined): string;
  encodeFunctionData(
    functionFragment: 'transferOwnership',
    values: [string]
  ): string;
  encodeFunctionData(functionFragment: 'addAlias', values: [string]): string;
  encodeFunctionData(
    functionFragment: 'addPermittedStaker',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'addPermittedStakers',
    values: [string[]]
  ): string;
  encodeFunctionData(functionFragment: 'balanceOf', values: [string]): string;
  encodeFunctionData(
    functionFragment: 'checkStakingAmounts',
    values: [string]
  ): string;
  encodeFunctionData(functionFragment: 'getReward', values: [string]): string;
  encodeFunctionData(
    functionFragment: 'getStakingAddress',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'getTokenAddress',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'isPermittedStaker',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'maximumStake',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'minimumStake',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'penalizeTokens',
    values: [BigNumberish, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'permittedStakersOn',
    values?: undefined
  ): string;
  encodeFunctionData(functionFragment: 'removeAlias', values: [string]): string;
  encodeFunctionData(
    functionFragment: 'removePermittedStaker',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'restakePenaltyTokens',
    values: [string, BigNumberish]
  ): string;
  encodeFunctionData(functionFragment: 'rewardOf', values: [string]): string;
  encodeFunctionData(
    functionFragment: 'rewardValidator',
    values: [BigNumberish, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'setContractResolver',
    values: [string]
  ): string;
  encodeFunctionData(
    functionFragment: 'setMaxAliasCount',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setMaximumStake',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setMinimumStake',
    values: [BigNumberish]
  ): string;
  encodeFunctionData(
    functionFragment: 'setPermittedStakersOn',
    values: [boolean]
  ): string;
  encodeFunctionData(
    functionFragment: 'stake',
    values: [BigNumberish, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'totalStaked',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'transferPenaltyTokens',
    values: [BigNumberish, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'withdraw(uint256,address)',
    values: [BigNumberish, string]
  ): string;
  encodeFunctionData(
    functionFragment: 'withdraw()',
    values?: undefined
  ): string;
  encodeFunctionData(
    functionFragment: 'withdrawPenaltyTokens',
    values: [BigNumberish]
  ): string;

  decodeFunctionResult(functionFragment: 'diamondCut', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'facetAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'facetAddresses',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'facetFunctionSelectors',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'facets', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'supportsInterface',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'owner', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'transferOwnership',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'addAlias', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'addPermittedStaker',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'addPermittedStakers',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'balanceOf', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'checkStakingAmounts',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'getReward', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'getStakingAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'getTokenAddress',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'isPermittedStaker',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'maximumStake',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'minimumStake',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'penalizeTokens',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'permittedStakersOn',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'removeAlias',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'removePermittedStaker',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'restakePenaltyTokens',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'rewardOf', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'rewardValidator',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setContractResolver',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setMaxAliasCount',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setMaximumStake',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setMinimumStake',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'setPermittedStakersOn',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'stake', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'totalStaked',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'transferPenaltyTokens',
    data: BytesLike
  ): Result;
  decodeFunctionResult(
    functionFragment: 'withdraw(uint256,address)',
    data: BytesLike
  ): Result;
  decodeFunctionResult(functionFragment: 'withdraw()', data: BytesLike): Result;
  decodeFunctionResult(
    functionFragment: 'withdrawPenaltyTokens',
    data: BytesLike
  ): Result;

  events: {
    'DiamondCut((address,uint8,bytes4[])[],address,bytes)': EventFragment;
    'OwnershipTransferred(address,address)': EventFragment;
    'AliasAdded(address,address)': EventFragment;
    'AliasRemoved(address,address)': EventFragment;
    'MaxAliasCountSet(uint256)': EventFragment;
    'MaximumStakeSet(uint256)': EventFragment;
    'MinimumStakeSet(uint256)': EventFragment;
    'PermittedStakerAdded(address)': EventFragment;
    'PermittedStakerRemoved(address)': EventFragment;
    'PermittedStakersOnChanged(bool)': EventFragment;
    'ResolverContractAddressSet(address)': EventFragment;
    'RewardPaid(address,uint256)': EventFragment;
    'Staked(address,uint256)': EventFragment;
    'TokenRewardPerTokenPerEpochSet(uint256)': EventFragment;
    'ValidatorNotRewardedBecauseAlias(address,address)': EventFragment;
    'ValidatorRewarded(address,uint256)': EventFragment;
    'ValidatorTokensPenalized(address,uint256)': EventFragment;
    'Withdrawn(address,uint256)': EventFragment;
  };

  getEvent(nameOrSignatureOrTopic: 'DiamondCut'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'OwnershipTransferred'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'AliasAdded'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'AliasRemoved'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'MaxAliasCountSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'MaximumStakeSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'MinimumStakeSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'PermittedStakerAdded'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'PermittedStakerRemoved'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'PermittedStakersOnChanged'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'ResolverContractAddressSet'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'RewardPaid'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Staked'): EventFragment;
  getEvent(
    nameOrSignatureOrTopic: 'TokenRewardPerTokenPerEpochSet'
  ): EventFragment;
  getEvent(
    nameOrSignatureOrTopic: 'ValidatorNotRewardedBecauseAlias'
  ): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'ValidatorRewarded'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'ValidatorTokensPenalized'): EventFragment;
  getEvent(nameOrSignatureOrTopic: 'Withdrawn'): EventFragment;
}

export interface DiamondCutEventObject {
  _diamondCut: IDiamond.FacetCutStructOutput[];
  _init: string;
  _calldata: string;
}
export type DiamondCutEvent = TypedEvent<
  [IDiamond.FacetCutStructOutput[], string, string],
  DiamondCutEventObject
>;

export type DiamondCutEventFilter = TypedEventFilter<DiamondCutEvent>;

export interface OwnershipTransferredEventObject {
  previousOwner: string;
  newOwner: string;
}
export type OwnershipTransferredEvent = TypedEvent<
  [string, string],
  OwnershipTransferredEventObject
>;

export type OwnershipTransferredEventFilter =
  TypedEventFilter<OwnershipTransferredEvent>;

export interface AliasAddedEventObject {
  staker: string;
  aliasAccount: string;
}
export type AliasAddedEvent = TypedEvent<
  [string, string],
  AliasAddedEventObject
>;

export type AliasAddedEventFilter = TypedEventFilter<AliasAddedEvent>;

export interface AliasRemovedEventObject {
  staker: string;
  aliasAccount: string;
}
export type AliasRemovedEvent = TypedEvent<
  [string, string],
  AliasRemovedEventObject
>;

export type AliasRemovedEventFilter = TypedEventFilter<AliasRemovedEvent>;

export interface MaxAliasCountSetEventObject {
  newMaxAliasCount: BigNumber;
}
export type MaxAliasCountSetEvent = TypedEvent<
  [BigNumber],
  MaxAliasCountSetEventObject
>;

export type MaxAliasCountSetEventFilter =
  TypedEventFilter<MaxAliasCountSetEvent>;

export interface MaximumStakeSetEventObject {
  newMaximumStake: BigNumber;
}
export type MaximumStakeSetEvent = TypedEvent<
  [BigNumber],
  MaximumStakeSetEventObject
>;

export type MaximumStakeSetEventFilter = TypedEventFilter<MaximumStakeSetEvent>;

export interface MinimumStakeSetEventObject {
  newMinimumStake: BigNumber;
}
export type MinimumStakeSetEvent = TypedEvent<
  [BigNumber],
  MinimumStakeSetEventObject
>;

export type MinimumStakeSetEventFilter = TypedEventFilter<MinimumStakeSetEvent>;

export interface PermittedStakerAddedEventObject {
  staker: string;
}
export type PermittedStakerAddedEvent = TypedEvent<
  [string],
  PermittedStakerAddedEventObject
>;

export type PermittedStakerAddedEventFilter =
  TypedEventFilter<PermittedStakerAddedEvent>;

export interface PermittedStakerRemovedEventObject {
  staker: string;
}
export type PermittedStakerRemovedEvent = TypedEvent<
  [string],
  PermittedStakerRemovedEventObject
>;

export type PermittedStakerRemovedEventFilter =
  TypedEventFilter<PermittedStakerRemovedEvent>;

export interface PermittedStakersOnChangedEventObject {
  permittedStakersOn: boolean;
}
export type PermittedStakersOnChangedEvent = TypedEvent<
  [boolean],
  PermittedStakersOnChangedEventObject
>;

export type PermittedStakersOnChangedEventFilter =
  TypedEventFilter<PermittedStakersOnChangedEvent>;

export interface ResolverContractAddressSetEventObject {
  newResolverAddress: string;
}
export type ResolverContractAddressSetEvent = TypedEvent<
  [string],
  ResolverContractAddressSetEventObject
>;

export type ResolverContractAddressSetEventFilter =
  TypedEventFilter<ResolverContractAddressSetEvent>;

export interface RewardPaidEventObject {
  staker: string;
  reward: BigNumber;
}
export type RewardPaidEvent = TypedEvent<
  [string, BigNumber],
  RewardPaidEventObject
>;

export type RewardPaidEventFilter = TypedEventFilter<RewardPaidEvent>;

export interface StakedEventObject {
  staker: string;
  amount: BigNumber;
}
export type StakedEvent = TypedEvent<[string, BigNumber], StakedEventObject>;

export type StakedEventFilter = TypedEventFilter<StakedEvent>;

export interface TokenRewardPerTokenPerEpochSetEventObject {
  newTokenRewardPerTokenPerEpoch: BigNumber;
}
export type TokenRewardPerTokenPerEpochSetEvent = TypedEvent<
  [BigNumber],
  TokenRewardPerTokenPerEpochSetEventObject
>;

export type TokenRewardPerTokenPerEpochSetEventFilter =
  TypedEventFilter<TokenRewardPerTokenPerEpochSetEvent>;

export interface ValidatorNotRewardedBecauseAliasEventObject {
  staker: string;
  aliasAccount: string;
}
export type ValidatorNotRewardedBecauseAliasEvent = TypedEvent<
  [string, string],
  ValidatorNotRewardedBecauseAliasEventObject
>;

export type ValidatorNotRewardedBecauseAliasEventFilter =
  TypedEventFilter<ValidatorNotRewardedBecauseAliasEvent>;

export interface ValidatorRewardedEventObject {
  staker: string;
  amount: BigNumber;
}
export type ValidatorRewardedEvent = TypedEvent<
  [string, BigNumber],
  ValidatorRewardedEventObject
>;

export type ValidatorRewardedEventFilter =
  TypedEventFilter<ValidatorRewardedEvent>;

export interface ValidatorTokensPenalizedEventObject {
  staker: string;
  amount: BigNumber;
}
export type ValidatorTokensPenalizedEvent = TypedEvent<
  [string, BigNumber],
  ValidatorTokensPenalizedEventObject
>;

export type ValidatorTokensPenalizedEventFilter =
  TypedEventFilter<ValidatorTokensPenalizedEvent>;

export interface WithdrawnEventObject {
  staker: string;
  amount: BigNumber;
}
export type WithdrawnEvent = TypedEvent<
  [string, BigNumber],
  WithdrawnEventObject
>;

export type WithdrawnEventFilter = TypedEventFilter<WithdrawnEvent>;

export interface StakingBalances extends BaseContract {
  connect(signerOrProvider: Signer | Provider | string): this;
  attach(addressOrName: string): this;
  deployed(): Promise<this>;

  interface: StakingBalancesInterface;

  queryFilter<TEvent extends TypedEvent>(
    event: TypedEventFilter<TEvent>,
    fromBlockOrBlockhash?: string | number | undefined,
    toBlock?: string | number | undefined
  ): Promise<Array<TEvent>>;

  listeners<TEvent extends TypedEvent>(
    eventFilter?: TypedEventFilter<TEvent>
  ): Array<TypedListener<TEvent>>;
  listeners(eventName?: string): Array<Listener>;
  removeAllListeners<TEvent extends TypedEvent>(
    eventFilter: TypedEventFilter<TEvent>
  ): this;
  removeAllListeners(eventName?: string): this;
  off: OnEvent<this>;
  on: OnEvent<this>;
  once: OnEvent<this>;
  removeListener: OnEvent<this>;

  functions: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<[string] & { facetAddress_: string }>;

    facetAddresses(
      overrides?: CallOverrides
    ): Promise<[string[]] & { facetAddresses_: string[] }>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<[string[]] & { _facetFunctionSelectors: string[] }>;

    facets(overrides?: CallOverrides): Promise<
      [IDiamondLoupe.FacetStructOutput[]] & {
        facets_: IDiamondLoupe.FacetStructOutput[];
      }
    >;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    owner(overrides?: CallOverrides): Promise<[string] & { owner_: string }>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    addAlias(
      aliasAccount: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    addPermittedStaker(
      staker: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    addPermittedStakers(
      stakers: string[],
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    balanceOf(account: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    checkStakingAmounts(
      account: string,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    getReward(
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    getStakingAddress(overrides?: CallOverrides): Promise<[string]>;

    getTokenAddress(overrides?: CallOverrides): Promise<[string]>;

    isPermittedStaker(
      staker: string,
      overrides?: CallOverrides
    ): Promise<[boolean]>;

    maximumStake(overrides?: CallOverrides): Promise<[BigNumber]>;

    minimumStake(overrides?: CallOverrides): Promise<[BigNumber]>;

    penalizeTokens(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    permittedStakersOn(overrides?: CallOverrides): Promise<[boolean]>;

    removeAlias(
      aliasAccount: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    removePermittedStaker(
      staker: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    restakePenaltyTokens(
      staker: string,
      balance: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    rewardOf(account: string, overrides?: CallOverrides): Promise<[BigNumber]>;

    rewardValidator(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setMaxAliasCount(
      newMaxAliasCount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setMaximumStake(
      newMaximumStake: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setMinimumStake(
      newMinimumStake: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    setPermittedStakersOn(
      permitted: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    stake(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    totalStaked(overrides?: CallOverrides): Promise<[BigNumber]>;

    transferPenaltyTokens(
      balance: BigNumberish,
      recipient: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    'withdraw(uint256,address)'(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    'withdraw()'(
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;

    withdrawPenaltyTokens(
      balance: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<ContractTransaction>;
  };

  diamondCut(
    _diamondCut: IDiamond.FacetCutStruct[],
    _init: string,
    _calldata: BytesLike,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  facetAddress(
    _functionSelector: BytesLike,
    overrides?: CallOverrides
  ): Promise<string>;

  facetAddresses(overrides?: CallOverrides): Promise<string[]>;

  facetFunctionSelectors(
    _facet: string,
    overrides?: CallOverrides
  ): Promise<string[]>;

  facets(overrides?: CallOverrides): Promise<IDiamondLoupe.FacetStructOutput[]>;

  supportsInterface(
    _interfaceId: BytesLike,
    overrides?: CallOverrides
  ): Promise<boolean>;

  owner(overrides?: CallOverrides): Promise<string>;

  transferOwnership(
    _newOwner: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  addAlias(
    aliasAccount: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  addPermittedStaker(
    staker: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  addPermittedStakers(
    stakers: string[],
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  balanceOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

  checkStakingAmounts(
    account: string,
    overrides?: CallOverrides
  ): Promise<boolean>;

  getReward(
    account: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  getStakingAddress(overrides?: CallOverrides): Promise<string>;

  getTokenAddress(overrides?: CallOverrides): Promise<string>;

  isPermittedStaker(
    staker: string,
    overrides?: CallOverrides
  ): Promise<boolean>;

  maximumStake(overrides?: CallOverrides): Promise<BigNumber>;

  minimumStake(overrides?: CallOverrides): Promise<BigNumber>;

  penalizeTokens(
    amount: BigNumberish,
    account: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  permittedStakersOn(overrides?: CallOverrides): Promise<boolean>;

  removeAlias(
    aliasAccount: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  removePermittedStaker(
    staker: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  restakePenaltyTokens(
    staker: string,
    balance: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  rewardOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

  rewardValidator(
    amount: BigNumberish,
    account: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setContractResolver(
    newResolverAddress: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setMaxAliasCount(
    newMaxAliasCount: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setMaximumStake(
    newMaximumStake: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setMinimumStake(
    newMinimumStake: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  setPermittedStakersOn(
    permitted: boolean,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  stake(
    amount: BigNumberish,
    account: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  totalStaked(overrides?: CallOverrides): Promise<BigNumber>;

  transferPenaltyTokens(
    balance: BigNumberish,
    recipient: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  'withdraw(uint256,address)'(
    amount: BigNumberish,
    account: string,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  'withdraw()'(
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  withdrawPenaltyTokens(
    balance: BigNumberish,
    overrides?: Overrides & { from?: string }
  ): Promise<ContractTransaction>;

  callStatic: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: CallOverrides
    ): Promise<void>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<string>;

    facetAddresses(overrides?: CallOverrides): Promise<string[]>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<string[]>;

    facets(
      overrides?: CallOverrides
    ): Promise<IDiamondLoupe.FacetStructOutput[]>;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<boolean>;

    owner(overrides?: CallOverrides): Promise<string>;

    transferOwnership(
      _newOwner: string,
      overrides?: CallOverrides
    ): Promise<void>;

    addAlias(aliasAccount: string, overrides?: CallOverrides): Promise<void>;

    addPermittedStaker(
      staker: string,
      overrides?: CallOverrides
    ): Promise<void>;

    addPermittedStakers(
      stakers: string[],
      overrides?: CallOverrides
    ): Promise<void>;

    balanceOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    checkStakingAmounts(
      account: string,
      overrides?: CallOverrides
    ): Promise<boolean>;

    getReward(account: string, overrides?: CallOverrides): Promise<void>;

    getStakingAddress(overrides?: CallOverrides): Promise<string>;

    getTokenAddress(overrides?: CallOverrides): Promise<string>;

    isPermittedStaker(
      staker: string,
      overrides?: CallOverrides
    ): Promise<boolean>;

    maximumStake(overrides?: CallOverrides): Promise<BigNumber>;

    minimumStake(overrides?: CallOverrides): Promise<BigNumber>;

    penalizeTokens(
      amount: BigNumberish,
      account: string,
      overrides?: CallOverrides
    ): Promise<void>;

    permittedStakersOn(overrides?: CallOverrides): Promise<boolean>;

    removeAlias(aliasAccount: string, overrides?: CallOverrides): Promise<void>;

    removePermittedStaker(
      staker: string,
      overrides?: CallOverrides
    ): Promise<void>;

    restakePenaltyTokens(
      staker: string,
      balance: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    rewardOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    rewardValidator(
      amount: BigNumberish,
      account: string,
      overrides?: CallOverrides
    ): Promise<void>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: CallOverrides
    ): Promise<void>;

    setMaxAliasCount(
      newMaxAliasCount: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setMaximumStake(
      newMaximumStake: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setMinimumStake(
      newMinimumStake: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;

    setPermittedStakersOn(
      permitted: boolean,
      overrides?: CallOverrides
    ): Promise<void>;

    stake(
      amount: BigNumberish,
      account: string,
      overrides?: CallOverrides
    ): Promise<void>;

    totalStaked(overrides?: CallOverrides): Promise<BigNumber>;

    transferPenaltyTokens(
      balance: BigNumberish,
      recipient: string,
      overrides?: CallOverrides
    ): Promise<void>;

    'withdraw(uint256,address)'(
      amount: BigNumberish,
      account: string,
      overrides?: CallOverrides
    ): Promise<void>;

    'withdraw()'(overrides?: CallOverrides): Promise<void>;

    withdrawPenaltyTokens(
      balance: BigNumberish,
      overrides?: CallOverrides
    ): Promise<void>;
  };

  filters: {
    'DiamondCut((address,uint8,bytes4[])[],address,bytes)'(
      _diamondCut?: null,
      _init?: null,
      _calldata?: null
    ): DiamondCutEventFilter;
    DiamondCut(
      _diamondCut?: null,
      _init?: null,
      _calldata?: null
    ): DiamondCutEventFilter;

    'OwnershipTransferred(address,address)'(
      previousOwner?: string | null,
      newOwner?: string | null
    ): OwnershipTransferredEventFilter;
    OwnershipTransferred(
      previousOwner?: string | null,
      newOwner?: string | null
    ): OwnershipTransferredEventFilter;

    'AliasAdded(address,address)'(
      staker?: string | null,
      aliasAccount?: null
    ): AliasAddedEventFilter;
    AliasAdded(
      staker?: string | null,
      aliasAccount?: null
    ): AliasAddedEventFilter;

    'AliasRemoved(address,address)'(
      staker?: string | null,
      aliasAccount?: null
    ): AliasRemovedEventFilter;
    AliasRemoved(
      staker?: string | null,
      aliasAccount?: null
    ): AliasRemovedEventFilter;

    'MaxAliasCountSet(uint256)'(
      newMaxAliasCount?: null
    ): MaxAliasCountSetEventFilter;
    MaxAliasCountSet(newMaxAliasCount?: null): MaxAliasCountSetEventFilter;

    'MaximumStakeSet(uint256)'(
      newMaximumStake?: null
    ): MaximumStakeSetEventFilter;
    MaximumStakeSet(newMaximumStake?: null): MaximumStakeSetEventFilter;

    'MinimumStakeSet(uint256)'(
      newMinimumStake?: null
    ): MinimumStakeSetEventFilter;
    MinimumStakeSet(newMinimumStake?: null): MinimumStakeSetEventFilter;

    'PermittedStakerAdded(address)'(
      staker?: null
    ): PermittedStakerAddedEventFilter;
    PermittedStakerAdded(staker?: null): PermittedStakerAddedEventFilter;

    'PermittedStakerRemoved(address)'(
      staker?: null
    ): PermittedStakerRemovedEventFilter;
    PermittedStakerRemoved(staker?: null): PermittedStakerRemovedEventFilter;

    'PermittedStakersOnChanged(bool)'(
      permittedStakersOn?: null
    ): PermittedStakersOnChangedEventFilter;
    PermittedStakersOnChanged(
      permittedStakersOn?: null
    ): PermittedStakersOnChangedEventFilter;

    'ResolverContractAddressSet(address)'(
      newResolverAddress?: null
    ): ResolverContractAddressSetEventFilter;
    ResolverContractAddressSet(
      newResolverAddress?: null
    ): ResolverContractAddressSetEventFilter;

    'RewardPaid(address,uint256)'(
      staker?: string | null,
      reward?: null
    ): RewardPaidEventFilter;
    RewardPaid(staker?: string | null, reward?: null): RewardPaidEventFilter;

    'Staked(address,uint256)'(
      staker?: string | null,
      amount?: null
    ): StakedEventFilter;
    Staked(staker?: string | null, amount?: null): StakedEventFilter;

    'TokenRewardPerTokenPerEpochSet(uint256)'(
      newTokenRewardPerTokenPerEpoch?: null
    ): TokenRewardPerTokenPerEpochSetEventFilter;
    TokenRewardPerTokenPerEpochSet(
      newTokenRewardPerTokenPerEpoch?: null
    ): TokenRewardPerTokenPerEpochSetEventFilter;

    'ValidatorNotRewardedBecauseAlias(address,address)'(
      staker?: string | null,
      aliasAccount?: null
    ): ValidatorNotRewardedBecauseAliasEventFilter;
    ValidatorNotRewardedBecauseAlias(
      staker?: string | null,
      aliasAccount?: null
    ): ValidatorNotRewardedBecauseAliasEventFilter;

    'ValidatorRewarded(address,uint256)'(
      staker?: string | null,
      amount?: null
    ): ValidatorRewardedEventFilter;
    ValidatorRewarded(
      staker?: string | null,
      amount?: null
    ): ValidatorRewardedEventFilter;

    'ValidatorTokensPenalized(address,uint256)'(
      staker?: string | null,
      amount?: null
    ): ValidatorTokensPenalizedEventFilter;
    ValidatorTokensPenalized(
      staker?: string | null,
      amount?: null
    ): ValidatorTokensPenalizedEventFilter;

    'Withdrawn(address,uint256)'(
      staker?: string | null,
      amount?: null
    ): WithdrawnEventFilter;
    Withdrawn(staker?: string | null, amount?: null): WithdrawnEventFilter;
  };

  estimateGas: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    facetAddresses(overrides?: CallOverrides): Promise<BigNumber>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    facets(overrides?: CallOverrides): Promise<BigNumber>;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    owner(overrides?: CallOverrides): Promise<BigNumber>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    addAlias(
      aliasAccount: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    addPermittedStaker(
      staker: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    addPermittedStakers(
      stakers: string[],
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    balanceOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    checkStakingAmounts(
      account: string,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    getReward(
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    getStakingAddress(overrides?: CallOverrides): Promise<BigNumber>;

    getTokenAddress(overrides?: CallOverrides): Promise<BigNumber>;

    isPermittedStaker(
      staker: string,
      overrides?: CallOverrides
    ): Promise<BigNumber>;

    maximumStake(overrides?: CallOverrides): Promise<BigNumber>;

    minimumStake(overrides?: CallOverrides): Promise<BigNumber>;

    penalizeTokens(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    permittedStakersOn(overrides?: CallOverrides): Promise<BigNumber>;

    removeAlias(
      aliasAccount: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    removePermittedStaker(
      staker: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    restakePenaltyTokens(
      staker: string,
      balance: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    rewardOf(account: string, overrides?: CallOverrides): Promise<BigNumber>;

    rewardValidator(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setMaxAliasCount(
      newMaxAliasCount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setMaximumStake(
      newMaximumStake: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setMinimumStake(
      newMinimumStake: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    setPermittedStakersOn(
      permitted: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    stake(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    totalStaked(overrides?: CallOverrides): Promise<BigNumber>;

    transferPenaltyTokens(
      balance: BigNumberish,
      recipient: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    'withdraw(uint256,address)'(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;

    'withdraw()'(overrides?: Overrides & { from?: string }): Promise<BigNumber>;

    withdrawPenaltyTokens(
      balance: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<BigNumber>;
  };

  populateTransaction: {
    diamondCut(
      _diamondCut: IDiamond.FacetCutStruct[],
      _init: string,
      _calldata: BytesLike,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    facetAddress(
      _functionSelector: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    facetAddresses(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    facetFunctionSelectors(
      _facet: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    facets(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    supportsInterface(
      _interfaceId: BytesLike,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    owner(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    transferOwnership(
      _newOwner: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    addAlias(
      aliasAccount: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    addPermittedStaker(
      staker: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    addPermittedStakers(
      stakers: string[],
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    balanceOf(
      account: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    checkStakingAmounts(
      account: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    getReward(
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    getStakingAddress(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    getTokenAddress(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    isPermittedStaker(
      staker: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    maximumStake(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    minimumStake(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    penalizeTokens(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    permittedStakersOn(
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    removeAlias(
      aliasAccount: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    removePermittedStaker(
      staker: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    restakePenaltyTokens(
      staker: string,
      balance: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    rewardOf(
      account: string,
      overrides?: CallOverrides
    ): Promise<PopulatedTransaction>;

    rewardValidator(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setContractResolver(
      newResolverAddress: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setMaxAliasCount(
      newMaxAliasCount: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setMaximumStake(
      newMaximumStake: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setMinimumStake(
      newMinimumStake: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    setPermittedStakersOn(
      permitted: boolean,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    stake(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    totalStaked(overrides?: CallOverrides): Promise<PopulatedTransaction>;

    transferPenaltyTokens(
      balance: BigNumberish,
      recipient: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    'withdraw(uint256,address)'(
      amount: BigNumberish,
      account: string,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    'withdraw()'(
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;

    withdrawPenaltyTokens(
      balance: BigNumberish,
      overrides?: Overrides & { from?: string }
    ): Promise<PopulatedTransaction>;
  };
}
