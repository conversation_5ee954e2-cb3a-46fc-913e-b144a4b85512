{"$schema": "https://typedoc.org/schema.json", "readme": "docs/Why-Vincent.md", "entryPoints": ["./src/index.ts"], "name": "<PERSON>", "projectDocuments": ["docs/Why-Vincent.md", "docs/Developers/Quick-Start.md", "docs/Developers/Custom-Tools.md", "docs/Developers/AI-Integration.md", "docs/Users/<USER>", "docs/Contact-Us.md"], "out": "./sdk-docs", "tsconfig": "./tsconfig.lib.json", "plugin": ["typedoc-material-theme", "typedoc-plugin-extras", "typedoc-plugin-zod"], "customTitle": "<PERSON>", "includeVersion": false, "navigation": {"includeCategories": true}, "defaultCategory": "API", "categorizeByGroup": false, "categoryOrder": ["Intro", "Developers", "Users", "Contact", "Vincent SDK API", "<PERSON> App", "<PERSON>"], "visibilityFilters": {}, "externalSymbolLinkMappings": {"@lit-protocol/types": {"*": "https://v7-api-doc-lit-js-sdk.vercel.app/modules/types_src.html"}, "@lit-protocol/pkp-ethers": {"*": "https://v7-api-doc-lit-js-sdk.vercel.app/modules/pkp_ethers_src.html"}, "did-jwt": {"*": "https://www.jsdocs.io/package/did-jwt"}, "ethers": {"*": "https://docs.ethers.org/v5/api/"}, "@ethersproject/abstract-signer": {"*": "https://docs.ethers.org/v5/api/"}}, "customCss": "./docs/src/css/custom.css"}