import { LIT_ERROR } from '@lit-protocol/constants';
import {
  AccessControlConditions,
  EvmContractConditions,
  SolRpcConditions,
  UnifiedAccessControlConditions,
  NodeClientErrorV1,
} from '@lit-protocol/types';

import {
  validateAccessControlConditionsSchema,
  validateEVMContractConditionsSchema,
  validateSolRpcConditionsSchema,
  validateUnifiedAccessControlConditionsSchema,
} from './validator';

describe('validator.ts', () => {
  it('should validate schema of an EVM Basic ACC', async () => {
    const evmBasicAccessControlConditions: AccessControlConditions = [
      {
        contractAddress: '******************************************',
        standardContractType: 'ERC1155',
        chain: 'ethereum',
        method: 'balanceOf',
        parameters: [':userAddress', '8'],
        returnValueTest: {
          comparator: '>',
          value: '0',
        },
      },
    ];

    expect(
      await validateAccessControlConditionsSchema(
        evmBasicAccessControlConditions
      )
    ).toBeTruthy();
  });

  it('should validate schema of a boolean expression ACCs', async () => {
    const evmBasicAccessControlConditions: AccessControlConditions = [
      {
        contractAddress: '******************************************',
        standardContractType: 'POAP',
        chain: 'xdai',
        method: 'eventId',
        parameters: [],
        returnValueTest: {
          comparator: '=',
          value: '37582',
        },
      },
      {
        operator: 'or',
      },
      {
        contractAddress: '******************************************',
        standardContractType: 'POAP',
        chain: 'ethereum',
        method: 'eventId',
        parameters: [],
        returnValueTest: {
          comparator: '=',
          value: '37582',
        },
      },
    ];

    expect(
      await validateAccessControlConditionsSchema(
        evmBasicAccessControlConditions
      )
    ).toBeTruthy();
  });

  it('should validate schema of a nested boolean expression of ACCs', async () => {
    const evmBasicAccessControlConditions: AccessControlConditions = [
      {
        contractAddress: '******************************************',
        standardContractType: 'ERC1155',
        chain: 'ethereum',
        method: 'balanceOf',
        parameters: [':userAddress', '8'],
        returnValueTest: {
          comparator: '>',
          value: '0',
        },
      },
      {
        operator: 'or',
      },
      [
        {
          contractAddress: '******************************************',
          standardContractType: 'POAP',
          chain: 'xdai',
          method: 'eventId',
          parameters: [],
          returnValueTest: {
            comparator: '=',
            value: '37582',
          },
        },
        {
          operator: 'or',
        },
        {
          contractAddress: '******************************************',
          standardContractType: 'POAP',
          chain: 'ethereum',
          method: 'eventId',
          parameters: [],
          returnValueTest: {
            comparator: '=',
            value: '37582',
          },
        },
      ],
    ];

    expect(
      await validateAccessControlConditionsSchema(
        evmBasicAccessControlConditions
      )
    ).toBeTruthy();
  });

  it('should validate schema of an EVM Contract ACC', async () => {
    const evmContractAccessControlConditions: EvmContractConditions = [
      {
        contractAddress: '******************************************',
        functionName: 'balanceOf',
        functionParams: [':userAddress', '8'],
        functionAbi: {
          type: 'function',
          stateMutability: 'view',
          outputs: [
            {
              type: 'uint256',
              name: '',
              internalType: 'uint256',
            },
          ],
          name: 'balanceOf',
          inputs: [
            {
              type: 'address',
              name: 'account',
              internalType: 'address',
            },
            {
              type: 'uint256',
              name: 'id',
              internalType: 'uint256',
            },
          ],
        },
        chain: 'ethereum',
        returnValueTest: {
          key: '',
          comparator: '>',
          value: '0',
        },
      },
    ];

    expect(
      await validateEVMContractConditionsSchema(
        evmContractAccessControlConditions
      )
    ).toBeTruthy();
  });

  it('should validate schema of a Solana ACC', async () => {
    const solAccessControlConditions: SolRpcConditions = [
      {
        method: 'getBalance',
        params: [':userAddress'],
        pdaParams: [],
        pdaInterface: { offset: 0, fields: {} },
        pdaKey: '',
        chain: 'solanaTestnet',
        returnValueTest: {
          key: '',
          comparator: '>=',
          value: '*********', // equals 0.1 SOL
        },
      },
    ];

    expect(
      await validateSolRpcConditionsSchema(solAccessControlConditions)
    ).toBeTruthy();
  });

  it('should validate schema of a Cosmos ACC', async () => {
    const cosmosAccessControlConditions: UnifiedAccessControlConditions = [
      {
        conditionType: 'cosmos',
        path: ':userAddress',
        chain: 'cosmos',
        returnValueTest: {
          key: '',
          comparator: '=',
          value: 'cosmos1vn6zl0924yj86jrp330wcwjclzdharljq03a8h',
        },
      },
    ];

    expect(
      await validateUnifiedAccessControlConditionsSchema(
        cosmosAccessControlConditions
      )
    ).toBeTruthy();
  });

  it('should validate schema of a set of unified ACCs', async () => {
    const unifiedAccessControlConditions: UnifiedAccessControlConditions = [
      {
        conditionType: 'solRpc',
        method: 'getBalance',
        params: [':userAddress'],
        chain: 'solana',
        pdaParams: [],
        pdaInterface: { offset: 0, fields: {} },
        pdaKey: '',
        returnValueTest: {
          key: '',
          comparator: '>=',
          value: '*********', // equals 0.1 SOL
        },
      },
      { operator: 'or' },
      {
        conditionType: 'evmBasic',
        contractAddress: '',
        standardContractType: '',
        chain: 'ethereum',
        method: 'eth_getBalance',
        parameters: [':userAddress', 'latest'],
        returnValueTest: {
          comparator: '>=',
          value: '*********00000',
        },
      },
      { operator: 'or' },
      {
        conditionType: 'evmContract',
        contractAddress: '******************************************',
        functionName: 'balanceOf',
        functionParams: [':userAddress', '8'],
        functionAbi: {
          type: 'function',
          stateMutability: 'view',
          outputs: [
            {
              type: 'uint256',
              name: '',
              internalType: 'uint256',
            },
          ],
          name: 'balanceOf',
          inputs: [
            {
              type: 'address',
              name: 'account',
              internalType: 'address',
            },
            {
              type: 'uint256',
              name: 'id',
              internalType: 'uint256',
            },
          ],
        },
        chain: 'polygon',
        returnValueTest: {
          key: '',
          comparator: '>',
          value: '0',
        },
      },
    ];

    expect(
      await validateUnifiedAccessControlConditionsSchema(
        unifiedAccessControlConditions
      )
    ).toBeTruthy();
  });

  it('should throw when schema does not have all required fields', async () => {
    const evmBasicAccessControlConditions: AccessControlConditions = [
      {
        contractAddress: '',
        // standardContractType: '',
        // chain,
        // method: 'eth_getBalance',
        // parameters: [':userAddress', 'latest'],
        returnValueTest: {
          comparator: '>=',
          value: '0',
        },
      },
    ] as AccessControlConditions; // Explicit cast to override Typescript type checking

    let error: NodeClientErrorV1 | undefined;
    try {
      await validateAccessControlConditionsSchema(
        evmBasicAccessControlConditions
      );
    } catch (e) {
      error = e as NodeClientErrorV1;
    }

    expect(error).toBeDefined();
    expect(error!.errorKind).toBe(LIT_ERROR['INVALID_PARAM_TYPE'].kind);
    expect(error!.errorCode).toBe(LIT_ERROR['INVALID_PARAM_TYPE'].name);
  });

  it('should throw when schema has invalid fields', async () => {
    // Disable TS here to test invalid fields
    const evmBasicAccessControlConditions: AccessControlConditions = [
      {
        // @ts-ignore
        // eslint-disable-next-line @typescript-eslint/no-loss-of-precision
        contractAddress: ******************************************,
        // @ts-ignore
        standardContractType: 'AMM',
        // @ts-ignore
        chain: 'bitcoin',
        method: 'eth_getBalance',
        parameters: [':userAddress', 'latest'],
        returnValueTest: {
          comparator: '>=',
          value: '0',
        },
      },
    ];

    let error: NodeClientErrorV1 | undefined;
    try {
      await validateAccessControlConditionsSchema(
        evmBasicAccessControlConditions
      );
    } catch (e) {
      error = e as NodeClientErrorV1;
    }

    expect(error).toBeDefined();
    expect(error!.errorKind).toBe(LIT_ERROR['INVALID_PARAM_TYPE'].kind);
    expect(error!.errorCode).toBe(LIT_ERROR['INVALID_PARAM_TYPE'].name);
  });

  it('should throw when schema of a nested ACC does not validate', async () => {
    const evmBasicAccessControlConditions: AccessControlConditions = [
      {
        contractAddress: '',
        standardContractType: '',
        chain: 'ethereum',
        method: 'eth_getBalance',
        parameters: [':userAddress', 'latest'],
        returnValueTest: {
          comparator: '>=',
          value: '0',
        },
      },
      {
        operator: 'and',
      },
      [
        {
          contractAddress: '',
          standardContractType: '',
          chain: 'ethereum',
          method: 'eth_getBalance',
          parameters: [':userAddress', 'latest'],
          returnValueTest: {
            comparator: '>=',
            value: '1',
          },
        },
        {
          operator: 'and',
        },
        {
          contractAddress: '',
          // standardContractType: '',
          // chain,
          // method: 'eth_getBalance',
          // parameters: [':userAddress', 'latest'],
          returnValueTest: {
            comparator: '>=',
            value: '2',
          },
        },
      ],
    ] as AccessControlConditions; // Explicit cast to override Typescript type checking

    let error: NodeClientErrorV1 | undefined;
    try {
      await validateAccessControlConditionsSchema(
        evmBasicAccessControlConditions
      );
    } catch (e) {
      error = e as NodeClientErrorV1;
    }

    expect(error).toBeDefined();
    expect(error!.errorKind).toBe(LIT_ERROR['INVALID_PARAM_TYPE'].kind);
    expect(error!.errorCode).toBe(LIT_ERROR['INVALID_PARAM_TYPE'].name);
  });
});
