import { LIT_ABILITY_VALUES } from '@lit-protocol/constants';
import { ILitResource } from '@lit-protocol/types';

// This is here to prevent circular dependency issue
export interface AuthSig {
  sig: any;
  derivedVia: string;
  signedMessage: string;
  address: string;
}
export type PlainJSON =
  | boolean
  | number
  | string
  | { [key: string]: PlainJSON }
  | Array<PlainJSON>;
export type AttenuationsObject = {
  [key: string]: { [key: string]: Array<PlainJSON> };
};
export type CID = string;

/**
 * A LIT resource ability is a combination of a LIT resource and a LIT ability.
 * It specifies which LIT specific ability is being requested to be performed
 * on the specified LIT resource.
 *
 * @description This object does NOT guarantee compatibility between the
 * specified LIT resource and the specified LIT ability, and will be validated by
 * the LIT-internal systems.
 */
export type LitResourceAbilityRequest = {
  resource: ILitResource;
  ability: LIT_ABILITY_VALUES;
  data?: any;
};
