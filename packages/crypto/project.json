{"name": "crypto", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/crypto/src", "projectType": "library", "implicitDependencies": ["wasm"], "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/crypto", "main": "packages/crypto/src/index.ts", "tsConfig": "packages/crypto/tsconfig.lib.json", "assets": ["packages/crypto/*.md"], "updateBuildableProjectDepsInPackageJson": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/crypto/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/crypto"], "options": {"jestConfig": "packages/crypto/jest.config.ts", "passWithNoTests": true}}, "testWatch": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/crypto"], "options": {"jestConfig": "packages/crypto/jest.config.ts", "passWithNoTests": true, "watch": true}}}, "tags": []}