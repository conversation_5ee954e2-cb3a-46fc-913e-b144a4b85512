{"name": "@lit-protocol/misc-browser", "license": "MIT", "homepage": "https://github.com/Lit-Protocol/js-sdk", "repository": {"type": "git", "url": "https://github.com/LIT-Protocol/js-sdk"}, "keywords": ["library"], "bugs": {"url": "https://github.com/LIT-Protocol/js-sdk/issues"}, "type": "commonjs", "publishConfig": {"access": "public", "directory": "../../dist/packages/misc-browser"}, "gitHead": "0d7334c2c55f448e91fe32f29edc5db8f5e09e4b", "tags": ["browser"], "version": "7.1.1", "main": "./dist/src/index.js", "typings": "./dist/src/index.d.ts"}