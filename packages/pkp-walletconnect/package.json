{"name": "@lit-protocol/pkp-walletconnect", "version": "7.1.1", "type": "commonjs", "license": "MIT", "homepage": "https://github.com/Lit-Protocol/js-sdk", "repository": {"type": "git", "url": "https://github.com/LIT-Protocol/js-sdk"}, "keywords": ["library"], "bugs": {"url": "https://github.com/LIT-Protocol/js-sdk/issues"}, "publishConfig": {"access": "public", "directory": "../../dist/packages/pkp-walletconnect"}, "browser": {"crypto": false, "stream": false}, "peerDependencies": {"@walletconnect/core": "2.9.2", "@walletconnect/jsonrpc-utils": "1.0.8", "@walletconnect/types": "2.9.2", "@walletconnect/utils": "2.9.2", "@walletconnect/web3wallet": "1.8.8"}, "tags": ["universal"], "main": "./dist/src/index.js", "typings": "./dist/src/index.d.ts"}