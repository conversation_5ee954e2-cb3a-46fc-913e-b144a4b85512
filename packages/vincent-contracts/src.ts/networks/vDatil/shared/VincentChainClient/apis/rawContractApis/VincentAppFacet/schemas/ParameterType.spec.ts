import { hexEncodedParameterValueSchema } from './ParameterType';

describe('hexEncodedParameterValueSchema', () => {
  it('should correctly encode different parameter types', () => {
    const input = [
      [
        [
          { type: 'int256', value: '-1000000000000000000' },
          {
            type: 'int256[]',
            value: '-1000000000000000000,2000000000000000000',
          },
          { type: 'uint256', value: '1000000000000000000' },
          {
            type: 'uint256[]',
            value: '1000000000000000000,2000000000000000000',
          },
          { type: 'bool', value: 'true' },
          { type: 'bool[]', value: 'true,false,true' },
          {
            type: 'address',
            value: '0x1234567890123456789012345678901234567890',
          },
          {
            type: 'address[]',
            value:
              '0x1234567890123456789012345678901234567890,0x0987654321098765432109876543210987654321',
          },
          { type: 'string', value: 'Hello World' },
          { type: 'string[]', value: 'Hello,World' },
          { type: 'bytes', value: '0x1234' },
          { type: 'bytes[]', value: '0x1234,0x5678' },
        ],
      ],
    ];

    const output = hexEncodedParameterValueSchema.parse(input);

    console.log('output', output);

    expect(output).toEqual([
      [
        [
          '0xfffffffffffffffffffffffffffffffffffffffffffffffff21f494c589c0000',
          '0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002fffffffffffffffffffffffffffffffffffffffffffffffff21f494c589c00000000000000000000000000000000000000000000000000001bc16d674ec80000',
          '0x0000000000000000000000000000000000000000000000000de0b6b3a7640000',
          '0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000de0b6b3a76400000000000000000000000000000000000000000000000000001bc16d674ec80000',
          '0x0000000000000000000000000000000000000000000000000000000000000001',
          '0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000003000000000000000000000000000000000000000000000000000000000000000100000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000001',
          '0x0000000000000000000000001234567890123456789012345678901234567890',
          '0x0000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000200000000000000000000000012345678901234567890123456789012345678900000000000000000000000000987654321098765432109876543210987654321',
          '0x0000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000b48656c6c6f20576f726c64000000000000000000000000000000000000000000',
          '0x0000000000000000000000000000000000000000000000000000000000000020000000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000400000000000000000000000000000000000000000000000000000000000000080000000000000000000000000000000000000000000000000000000000000000548656c6c6f0000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000005576f726c64000000000000000000000000000000000000000000000000000000',
          '0x000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000000021234000000000000000000000000000000000000000000000000000000000000',
          '0x00000000000000000000000000000000000000000000000000000000000000200000000000000000000000000000000000000000000000000000000000000002000000000000000000000000000000000000000000000000000000000000004000000000000000000000000000000000000000000000000000000000000000800000000000000000000000000000000000000000000000000000000000000002123400000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000025678000000000000000000000000000000000000000000000000000000000000',
        ],
      ],
    ]);
  });
});
