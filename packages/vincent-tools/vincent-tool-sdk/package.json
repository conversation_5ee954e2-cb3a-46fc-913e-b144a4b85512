{"name": "@lit-protocol/vincent-tool-sdk", "version": "0.0.1-0", "publishConfig": {"access": "public"}, "dependencies": {"@lit-protocol/constants": "7.0.6", "tslib": "^2.3.0", "zod": "^3.24.1"}, "type": "commonjs", "main": "./dist/index.js", "typings": "./dist/index.d.ts", "files": ["dist", "!**/*.tsbuildinfo"], "nx": {"sourceRoot": "packages/vincent-tool-sdk/src", "projectType": "library", "name": "vincent-tool-sdk"}, "devDependencies": {"jsonc-eslint-parser": "^2.4.0"}}