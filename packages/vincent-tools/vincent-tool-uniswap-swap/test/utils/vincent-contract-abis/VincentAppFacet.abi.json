[{"type": "function", "name": "addAuthorizedRedirectUri", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "redirectUri", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "addDelegatee", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "delegatee", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "deleteApp", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "enableAppVersion", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "appVersion", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "registerApp", "inputs": [{"name": "appInfo", "type": "tuple", "internalType": "struct VincentAppFacet.AppInfo", "components": [{"name": "name", "type": "string", "internalType": "string"}, {"name": "description", "type": "string", "internalType": "string"}, {"name": "deploymentStatus", "type": "uint8", "internalType": "enum VincentAppStorage.DeploymentStatus"}, {"name": "authorizedRedirectUris", "type": "string[]", "internalType": "string[]"}, {"name": "delegatees", "type": "address[]", "internalType": "address[]"}]}, {"name": "versionTools", "type": "tuple", "internalType": "struct VincentAppFacet.AppVersionTools", "components": [{"name": "toolIpfsCids", "type": "string[]", "internalType": "string[]"}, {"name": "toolPolicies", "type": "string[][]", "internalType": "string[][]"}, {"name": "toolPolicyParameterNames", "type": "string[][][]", "internalType": "string[][][]"}, {"name": "toolPolicyParameterTypes", "type": "uint8[][][]", "internalType": "enum VincentAppStorage.ParameterType[][][]"}]}], "outputs": [{"name": "newAppId", "type": "uint256", "internalType": "uint256"}, {"name": "newAppVersion", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "registerNextAppVersion", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "versionTools", "type": "tuple", "internalType": "struct VincentAppFacet.AppVersionTools", "components": [{"name": "toolIpfsCids", "type": "string[]", "internalType": "string[]"}, {"name": "toolPolicies", "type": "string[][]", "internalType": "string[][]"}, {"name": "toolPolicyParameterNames", "type": "string[][][]", "internalType": "string[][][]"}, {"name": "toolPolicyParameterTypes", "type": "uint8[][][]", "internalType": "enum VincentAppStorage.ParameterType[][][]"}]}], "outputs": [{"name": "newAppVersion", "type": "uint256", "internalType": "uint256"}], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeAuthorizedRedirectUri", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "redirectUri", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "removeDelegatee", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "delegatee", "type": "address", "internalType": "address"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateAppDeploymentStatus", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "deploymentStatus", "type": "uint8", "internalType": "enum VincentAppStorage.DeploymentStatus"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateAppDescription", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "newDescription", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "function", "name": "updateAppName", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "newName", "type": "string", "internalType": "string"}], "outputs": [], "stateMutability": "nonpayable"}, {"type": "event", "name": "AppDeleted", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}], "anonymous": false}, {"type": "event", "name": "AppDeploymentStatusUpdated", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "deploymentStatus", "type": "uint8", "indexed": true, "internalType": "uint8"}], "anonymous": false}, {"type": "event", "name": "AppDescriptionUpdated", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "newDescription", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "AppEnabled", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "appVersion", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "enabled", "type": "bool", "indexed": true, "internalType": "bool"}], "anonymous": false}, {"type": "event", "name": "AppNameUpdated", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "newName", "type": "string", "indexed": false, "internalType": "string"}], "anonymous": false}, {"type": "event", "name": "AuthorizedRedirectUriAdded", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "hashedRedirectUri", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "AuthorizedRedirectUriRemoved", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "hashedRedirectUri", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "event", "name": "DelegateeAdded", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "delegatee", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "DelegateeRemoved", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "delegatee", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewAppRegistered", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "manager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewAppVersionRegistered", "inputs": [{"name": "appId", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "appVersion", "type": "uint256", "indexed": true, "internalType": "uint256"}, {"name": "manager", "type": "address", "indexed": true, "internalType": "address"}], "anonymous": false}, {"type": "event", "name": "NewLitActionRegistered", "inputs": [{"name": "litActionIpfsCidHash", "type": "bytes32", "indexed": true, "internalType": "bytes32"}], "anonymous": false}, {"type": "error", "name": "AppAlreadyInRequestedDeploymentStatus", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "deploymentStatus", "type": "uint8", "internalType": "uint8"}]}, {"type": "error", "name": "AppHasBeenDeleted", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "AppNotRegistered", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "AppVersionAlreadyInRequestedState", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "appVersion", "type": "uint256", "internalType": "uint256"}, {"name": "enabled", "type": "bool", "internalType": "bool"}]}, {"type": "error", "name": "AppVersionHasDelegatedAgents", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "appVersion", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "AppVersionNotRegistered", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "appVersion", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "CannotRemoveLastRedirectUri", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "DelegateeAlreadyRegisteredToApp", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "delegatee", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "DelegateeNotRegisteredToApp", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "delegatee", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "EmptyAppDescriptionNotAllowed", "inputs": []}, {"type": "error", "name": "EmptyAppNameNotAllowed", "inputs": []}, {"type": "error", "name": "EmptyParameterNameNotAllowed", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "toolIndex", "type": "uint256", "internalType": "uint256"}, {"name": "policyIndex", "type": "uint256", "internalType": "uint256"}, {"name": "paramIndex", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "EmptyPolicyIpfsCidNotAllowed", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "toolIndex", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "EmptyRedirectUriNotAllowed", "inputs": []}, {"type": "error", "name": "EmptyToolIpfsCidNotAllowed", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "toolIndex", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "NoRedirectUrisProvided", "inputs": []}, {"type": "error", "name": "NoToolsProvided", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "NotAppManager", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "msgSender", "type": "address", "internalType": "address"}]}, {"type": "error", "name": "ParameterArrayLengthMismatch", "inputs": [{"name": "toolIndex", "type": "uint256", "internalType": "uint256"}, {"name": "policyIndex", "type": "uint256", "internalType": "uint256"}, {"name": "paramNamesLength", "type": "uint256", "internalType": "uint256"}, {"name": "paramTypes<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "PolicyArrayLengthMismatch", "inputs": [{"name": "toolIndex", "type": "uint256", "internalType": "uint256"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "paramNamesLength", "type": "uint256", "internalType": "uint256"}, {"name": "paramTypes<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "RedirectUriAlreadyAuthorizedForApp", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "redirectUri", "type": "string", "internalType": "string"}]}, {"type": "error", "name": "RedirectUriNotRegisteredToApp", "inputs": [{"name": "appId", "type": "uint256", "internalType": "uint256"}, {"name": "redirectUri", "type": "string", "internalType": "string"}]}, {"type": "error", "name": "ToolArrayDimensionMismatch", "inputs": [{"name": "toolsLength", "type": "uint256", "internalType": "uint256"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}, {"name": "paramNamesLength", "type": "uint256", "internalType": "uint256"}, {"name": "paramTypes<PERSON><PERSON><PERSON>", "type": "uint256", "internalType": "uint256"}]}, {"type": "error", "name": "ZeroAddressDelegateeNotAllowed", "inputs": []}]