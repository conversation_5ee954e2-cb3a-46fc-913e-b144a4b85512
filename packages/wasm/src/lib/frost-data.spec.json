{"messageHex": "74657374", "publicKeyHex": "899196af442a2c0d32d9c18b837a838379db18b37148bf35a4917202e0214658", "shares": [{"identifierHex": "0100000000000000000000000000000000000000000000000000000000000000", "hidingNonceHex": "8ded48acb6cb53aecc4c3db42881d68139899e87b3eee9eabd87d05a685d046d", "bindingNonceHex": "2371452b8cce8907c5a056f468dad53149334de2098000a3f9c98badf48d99a0", "signatureShareHex": "a8272cf614b6af2c178575792574c438ad9b617d3aca925edd4f58419d307304", "verifyingShareHex": "270e65d2e7d990c24d376b5fe008bcefe8638af62d38971e67b4c89bd2bdec07"}, {"identifierHex": "0200000000000000000000000000000000000000000000000000000000000000", "hidingNonceHex": "a895aa9a8e588caeb89d765c738df48a5f4be3fa6b91b953e0b7bce5074c54fc", "bindingNonceHex": "e3b026a1b011c7e6a9d09ce2b4945cbac261a61ad2f43234993c12edf63a630c", "signatureShareHex": "943afc49d0397adfea011b78f4963543be476aea4d1e4a35afb915bba3721c09", "verifyingShareHex": "70f3807ea1c784f36fc900158a1c8ec3aaff7026be02e8edc0a2237c1eb73ccb"}, {"identifierHex": "0300000000000000000000000000000000000000000000000000000000000000", "hidingNonceHex": "7762508c2d030f72359daf77e82c9ecdc99d39a2f36f7d9cbc69ba9153e85013", "bindingNonceHex": "1c2172836dc0b927e3d226458bd0be8d624cacca13fa82a258367eb025f41a38", "signatureShareHex": "2527bed7775274fd49c72e94beddb2cb16be356db29ac5b8a1bc795fc714e402", "verifyingShareHex": "3d6b6fdc64465c5d515770211fa981b799e3237b5d7023bf7f6a7e370add3ea7"}], "signatureHex": "d02bfc10cdc5d7cd81eca05d5272dbf7f37a76062addd09626b5c9252bfda32974b5f0ba42df8bb175b1c7e2f9eecd3282a101d53a83a24c2ec6e75b08b87300"}