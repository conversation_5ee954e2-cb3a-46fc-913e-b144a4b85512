{"name": "@lit-protocol/pkp-base", "version": "7.1.1", "type": "commonjs", "license": "MIT", "homepage": "https://github.com/Lit-Protocol/js-sdk", "repository": {"type": "git", "url": "https://github.com/LIT-Protocol/js-sdk"}, "keywords": ["library"], "bugs": {"url": "https://github.com/LIT-Protocol/js-sdk/issues"}, "publishConfig": {"access": "public", "directory": "../../dist/packages/pkp-base"}, "browser": {"crypto": false, "stream": false}, "tags": ["universal"], "main": "./dist/src/index.js", "typings": "./dist/src/index.d.ts"}