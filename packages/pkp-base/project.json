{"name": "pkp-base", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pkp-base/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pkp-base", "main": "packages/pkp-base/src/index.ts", "tsConfig": "packages/pkp-base/tsconfig.lib.json", "assets": ["packages/pkp-base/*.md"], "updateBuildableProjectDepsInPackageJson": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/pkp-base/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/pkp-base"], "options": {"jestConfig": "packages/pkp-base/jest.config.ts", "passWithNoTests": true}}}, "tags": []}