import { formatSessionSigs } from './session-sigs-reader';

const MOCK_VALID_SESSION_SIGS = {
  'https://************:443': {
    sig: '80c9a32ea9bc027f4dfffeb8236c3a6acc5548c3f730feb39151d4711ade2f94e660358679d0be8fb6117efaf06a77d5f262649f687d319cf8430a787f67bd0c',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '8cd8e12fdac591888f564d942a5745d7704749b5c2651e4a5360643a919bc24c00887b0d9ca885ac5f716a5d21436a7d4cdd172ba9b3ac0833cc1b4ca2a67207',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://*************:443': {
    sig: '3683aeb96f59be4f6bc077ec77c4008905f82b1a5436ff0554697d33c7ed0deb7ae91e6c5bf810e7f6fde40cc49e5b423d73d19d5763d835b4819c50bc90da0f',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://*************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: 'f5541138424b81172ca314278750be24f87c86cef3593e5f18dc77737e814ea1dead6291fdffb699b3a550543fb7968a7fbfaf3e9f5b3f768dc347ab95bf160a',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '5f84a6ec4baa73341146be9239b43d7848e6d657fd121b3934a5496d4a117f164baab1796904305fb2a0fefc5a1fa4dd1297540c8ff5d544ca62a89b7f64d902',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '11428a4bf4364ceb0a4cbad9c01445c146c20a9aee0e71dcec580231ddbbde71da08bc0d6ceed4b26fab72b4a107d37236c738734058f55854758da70b70b804',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://***************:443': {
    sig: '5c71bf6752510ef0fb15d9db968e0a179122f92d220d0f3f789b7b8159b5daf7c56cad6a6e0b82aa3bf6e8d31106b769e05da798be6b4d7832457d13a65ed201',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://***************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '00cc6e45412caf5ffe29e7169a29bc1ddf9023a1d9efa08cf1b8d0ee1eb32decb7920efa569618dcfc3954d3a75efed699c98bb2b2ae538933cfb462caebc101',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '579ead535757f22937e30c0ce77e3e79e40d468d220114cf7ad040b91dbf368069483ecb2cafe64669780b2535139b909d9b0cfb08190654355819f10d60320a',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:48:13.383Z\\nExpiration Time: 2024-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2024-10-16T13:47:47Z\\nExpiration Time: 2024-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2024-10-16T13:48:18.135Z","expiration":"2024-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
};

const MOCK_EXPIRED_SESSION_SIGS = {
  'https://************:443': {
    sig: '80c9a32ea9bc027f4dfffeb8236c3a6acc5548c3f730feb39151d4711ade2f94e660358679d0be8fb6117efaf06a77d5f262649f687d319cf8430a787f67bd0c',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '8cd8e12fdac591888f564d942a5745d7704749b5c2651e4a5360643a919bc24c00887b0d9ca885ac5f716a5d21436a7d4cdd172ba9b3ac0833cc1b4ca2a67207',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://*************:443': {
    sig: '3683aeb96f59be4f6bc077ec77c4008905f82b1a5436ff0554697d33c7ed0deb7ae91e6c5bf810e7f6fde40cc49e5b423d73d19d5763d835b4819c50bc90da0f',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://*************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: 'f5541138424b81172ca314278750be24f87c86cef3593e5f18dc77737e814ea1dead6291fdffb699b3a550543fb7968a7fbfaf3e9f5b3f768dc347ab95bf160a',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '5f84a6ec4baa73341146be9239b43d7848e6d657fd121b3934a5496d4a117f164baab1796904305fb2a0fefc5a1fa4dd1297540c8ff5d544ca62a89b7f64d902',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '11428a4bf4364ceb0a4cbad9c01445c146c20a9aee0e71dcec580231ddbbde71da08bc0d6ceed4b26fab72b4a107d37236c738734058f55854758da70b70b804',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://***************:443': {
    sig: '5c71bf6752510ef0fb15d9db968e0a179122f92d220d0f3f789b7b8159b5daf7c56cad6a6e0b82aa3bf6e8d31106b769e05da798be6b4d7832457d13a65ed201',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://***************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '00cc6e45412caf5ffe29e7169a29bc1ddf9023a1d9efa08cf1b8d0ee1eb32decb7920efa569618dcfc3954d3a75efed699c98bb2b2ae538933cfb462caebc101',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
  'https://**************:443': {
    sig: '579ead535757f22937e30c0ce77e3e79e40d468d220114cf7ad040b91dbf368069483ecb2cafe64669780b2535139b909d9b0cfb08190654355819f10d60320a',
    derivedVia: 'litSessionSignViaNacl',
    signedMessage:
      '{"sessionKey":"8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da","resourceAbilityRequests":[{"resource":{"resource":"*","resourcePrefix":"lit-pkp"},"ability":"pkp-signing"},{"resource":{"resource":"*","resourcePrefix":"lit-litaction"},"ability":"lit-action-execution"}],"capabilities":[{"sig":"0x886ccec901d14a8eea6ba293e97588c070682e6cd987ee43678c3df466851f14795c027b3006887c6071f7e89122ca062a900803044d6e6b59aa16323ffb775f1b","derivedVia":"web3.eth.personal.sign","signedMessage":"localhost wants you to sign in with your Ethereum account:\\n******************************************\\n\\nThis is a test statement.  You can put anything you want here. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Auth\': \'Auth\' for \'lit-ratelimitincrease://25364\'.\\n\\nURI: lit:capability:delegation\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:48:13.383Z\\nExpiration Time: 2022-10-23T13:48:13.380Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LXJhdGVsaW1pdGluY3JlYXNlOi8vMjUzNjQiOnsiQXV0aC9BdXRoIjpbeyJuZnRfaWQiOlsiMjUzNjQiXX1dfX0sInByZiI6W119","address":"******************************************"},{"sig":"{\\"ProofOfPossession\\":\\"b868bcc867277cd160a60778acf0316a0c89c6b17127c5c2577ead9c25d073f6eefe4a1f7c342cd2b4e49648964354860813897273df3b78f2fe7cf00bbaad8e8586ef14c03f018c155827496f8ac385fc49fad21833dcd225bf8de53b2dd712\\"}","algo":"LIT_BLS","derivedVia":"lit.bls","signedMessage":"litprotocol.com wants you to sign in with your Ethereum account:\\n******************************************\\n\\nLit Protocol PKP session signature I further authorize the stated URI to perform the following actions on my behalf: I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. I further authorize the stated URI to perform the following actions on my behalf: (1) \'Threshold\': \'Execution\' for \'lit-litaction://*\'. (2) \'Threshold\': \'Signing\' for \'lit-pkp://*\'. (3) \'Auth\': \'Auth\' for \'lit-resolvedauthcontext://*\'.\\n\\nURI: lit:session:8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da\\nVersion: 1\\nChain ID: 1\\nNonce: 0xfba8cfeb4e933e68851e7b993e17cc7be919373ff0afce1d44db8ae4fe03436a\\nIssued At: 2022-10-16T13:47:47Z\\nExpiration Time: 2022-10-17T13:48:16.466Z\\nResources:\\n- urn:recap:eyJhdHQiOnsibGl0LWxpdGFjdGlvbjovLyoiOnsiVGhyZXNob2xkL0V4ZWN1dGlvbiI6W3t9XX0sImxpdC1wa3A6Ly8qIjp7IlRocmVzaG9sZC9TaWduaW5nIjpbe31dfSwibGl0LXJlc29sdmVkYXV0aGNvbnRleHQ6Ly8qIjp7IkF1dGgvQXV0aCI6W3siYXV0aF9jb250ZXh0Ijp7ImFjdGlvbklwZnNJZHMiOlsiUW1TSjN3NVRvck1xMnl2OEpkTVhKR1ZhZFdEZTN5Y3RHV3VqWmRjN3NGODhndCJdLCJhdXRoTWV0aG9kQ29udGV4dHMiOltdLCJhdXRoU2lnQWRkcmVzcyI6bnVsbCwiY3VzdG9tQXV0aFJlc291cmNlIjoiXCIodHJ1ZSwge1xcXCJNT1xcXCI6XFxcIkZPXFxcIixcXFwicHJpdmF0ZUtleVxcXCI6XFxcIjB4MzE5NGM0ZmYwMTQ4YWU1YzgzZTk0NGU0ZTY1NGIwM2JkNjYzYWRkNmM4NmY5MjIwNTUzNjZjNmYyMzA2YjFhN1xcXCJ9KVwiIiwicmVzb3VyY2VzIjpbXX19XX19LCJwcmYiOltdfQ","address":"******************************************"}],"issuedAt":"2022-10-16T13:48:18.135Z","expiration":"2022-10-17T13:48:16.466Z","nodeAddress":"https://**************:443"}',
    address: '8356d1e28349e2c8d1e7b3b85d7fa2913ad0c6961a39a5227c943839898870da',
    algo: 'ed25519',
  },
};

describe('formatSessionSigs', () => {
  it('should format session signatures correctly', () => {
    const currentTime = new Date('2022-01-01T06:00:00Z');
    const formattedSessionSigs = formatSessionSigs(
      JSON.stringify(MOCK_VALID_SESSION_SIGS),
      currentTime
    );

    const expectedOutput = `The request time is at: 2022-01-01T06:00:00.000Z
* Outer expiration:
    * Issued at: 2024-10-16T13:48:18.135Z
    * Expiration: 2024-10-17T13:48:16.466Z
    * Duration: 23 hours, 59 minutes, 58.331 seconds
    * Status: ✅ Not expired (valid for 1020 days)
* Capabilities:
    * Capability 1 (web3.eth.personal.sign):
        * Issued at: 2024-10-16T13:48:13.383Z
        * Expiration: 2024-10-23T13:48:13.380Z
        * Duration: 6 days
        * Status: ✅ Not expired (valid for 1026 days)
        * Attenuation:
            * lit-ratelimitincrease://25364
              * Auth/Auth
                * nft_id
                  * 25364
    * Capability 2 (lit.bls):
        * Issued at: 2024-10-16T13:47:47.000Z
        * Expiration: 2024-10-17T13:48:16.466Z
        * Duration: 1 days
        * Status: ✅ Not expired (valid for 1020 days)
        * Attenuation:
            * lit-litaction://*
              * Threshold/Execution
            * lit-pkp://*
              * Threshold/Signing
            * lit-resolvedauthcontext://*
              * Auth/Auth
                * auth_context
                  * actionIpfsIds
                    * QmSJ3w5TorMq2yv8JdMXJGVadWDe3yctGWujZdc7sF88gt
                  * authMethodContexts
                  * authSigAddress
                  * customAuthResource
                    * "(true, {\\"MO\\":\\"FO\\",\\"privateKey\\":\\"0x3194c4ff0148ae5c83e944e4e654b03bd663add6c86f922055366c6f2306b1a7\\"})"
                  * resources
`;

    expect(formattedSessionSigs).toBe(expectedOutput);
  });

  it('should handle expired session signatures correctly', () => {
    const currentTime = new Date('2024-01-01T06:00:00Z');
    const formattedSessionSigs = formatSessionSigs(
      JSON.stringify(MOCK_EXPIRED_SESSION_SIGS),
      currentTime
    );

    const expectedOutput = `The request time is at: 2024-01-01T06:00:00.000Z
* Outer expiration:
    * Issued at: 2022-10-16T13:48:18.135Z
    * Expiration: 2022-10-17T13:48:16.466Z
    * Duration: 23 hours, 59 minutes, 58.331 seconds
    * Status: ❌ Expired (expired 440 days ago)
* Capabilities:
    * Capability 1 (web3.eth.personal.sign):
        * Issued at: 2022-10-16T13:48:13.383Z
        * Expiration: 2022-10-23T13:48:13.380Z
        * Duration: 6 days
        * Status: ❌ Expired (expired 434 days ago)
        * Attenuation:
            * lit-ratelimitincrease://25364
              * Auth/Auth
                * nft_id
                  * 25364
    * Capability 2 (lit.bls):
        * Issued at: 2022-10-16T13:47:47.000Z
        * Expiration: 2022-10-17T13:48:16.466Z
        * Duration: 1 days
        * Status: ❌ Expired (expired 440 days ago)
        * Attenuation:
            * lit-litaction://*
              * Threshold/Execution
            * lit-pkp://*
              * Threshold/Signing
            * lit-resolvedauthcontext://*
              * Auth/Auth
                * auth_context
                  * actionIpfsIds
                    * QmSJ3w5TorMq2yv8JdMXJGVadWDe3yctGWujZdc7sF88gt
                  * authMethodContexts
                  * authSigAddress
                  * customAuthResource
                    * "(true, {\\"MO\\":\\"FO\\",\\"privateKey\\":\\"0x3194c4ff0148ae5c83e944e4e654b03bd663add6c86f922055366c6f2306b1a7\\"})"
                  * resources
`;
    expect(formattedSessionSigs).toBe(expectedOutput);
  });
});
