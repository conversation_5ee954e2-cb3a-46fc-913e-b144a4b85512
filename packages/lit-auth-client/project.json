{"name": "lit-auth-client", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/lit-auth-client/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/lit-auth-client", "main": "packages/lit-auth-client/src/index.ts", "tsConfig": "packages/lit-auth-client/tsconfig.lib.json", "assets": ["packages/lit-auth-client/*.md"], "updateBuildableProjectDepsInPackageJson": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/lit-auth-client/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/lit-auth-client"], "options": {"jestConfig": "packages/lit-auth-client/jest.config.ts", "passWithNoTests": true}}}, "tags": []}