import {
  AuthMethod,
  BaseProviderOptions,
  OAuthProviderOptions,
} from '@lit-protocol/types';
import {
  AUTH_METHOD_TYPE,
  UnauthorizedException,
  UnknownError,
} from '@lit-protocol/constants';
import { BaseProvider } from './BaseProvider';
import {
  prepareLoginUrl,
  parseLoginParams,
  getStateParam,
  decode,
  LIT_LOGIN_GATEWAY,
} from '../utils';
import { ethers } from 'ethers';

export default class DiscordProvider extends BaseProvider {
  /**
   * The redirect URI that Lit's login server should send the user back to
   */
  public redirectUri: string;
  /**
   * OAuth client ID. Defaults to one used by Lit
   */
  private clientId?: string;

  constructor(options: BaseProviderOptions & OAuthProviderOptions) {
    super(options);
    this.redirectUri = options.redirectUri || window.location.origin;
    this.clientId = options.clientId || '1052874239658692668';
  }

  /**
   * Redirect user to the Lit's Discord login page
   *
   * @param {Function} [callback] - Optional callback to handle login URL
   * @returns {Promise<void>} - Redirects user to Lit login page
   */
  public async signIn(callback?: (url: string) => void): Promise<void> {
    // Get login url
    const loginUrl = await prepareLoginUrl('discord', this.redirectUri);

    // If callback is provided, use it. Otherwise, redirect to login url
    if (callback) {
      callback(loginUrl);
    } else {
      window.location.assign(loginUrl);
    }
  }

  /**
   * Validate the URL parameters returned from Lit's login server and return the authentication data
   *
   * @returns {Promise<AuthMethod>} - Auth method object that contains OAuth token
   */
  public async authenticate(): Promise<AuthMethod> {
    // Check if current url matches redirect uri
    if (!window.location.href.startsWith(this.redirectUri)) {
      throw new UnauthorizedException(
        {
          info: {
            url: window.location.href,
            redirectUri: this.redirectUri,
          },
        },
        `Current url does not match provided redirect uri`
      );
    }

    // Check url for params
    const { provider, accessToken, state, error } = parseLoginParams(
      window.location.search
    );

    // Check if there's an error
    if (error) {
      throw new UnknownError(
        {
          info: {
            error,
          },
          cause: new Error(error),
        },
        error ?? 'Received error from discord authentication'
      );
    }

    // Check if provider is Discord
    if (!provider || provider !== 'discord') {
      throw new UnauthorizedException(
        {
          info: {
            provider,
            redirectUri: this.redirectUri,
          },
        },
        'OAuth provider does not match "discord"'
      );
    }

    // Check if state param matches
    if (!state || decode(decodeURIComponent(state)) !== getStateParam()) {
      throw new UnauthorizedException(
        {
          info: {
            state,
            redirectUri: this.redirectUri,
          },
        },
        'Invalid state parameter in callback URL'
      );
    }

    // Clear params from url
    window.history.replaceState(
      null,
      window.document.title,
      window.location.pathname
    );

    // Check if access token is present in url
    if (!accessToken) {
      throw new UnauthorizedException(
        {
          info: {
            accessToken,
            redirectUri: this.redirectUri,
          },
        },
        `Missing access token in callback URL`
      );
    }

    const authMethod = {
      authMethodType: AUTH_METHOD_TYPE.Discord,
      accessToken: accessToken,
    };
    return authMethod;
  }

  /**
   * Sign in using popup window
   *
   * @param baseURL
   */
  public async signInUsingPopup(baseURL: string): Promise<AuthMethod> {
    const width = 500;
    const height = 600;
    const left = window.screen.width / 2 - width / 2;
    const top = window.screen.height / 2 - height / 2;

    const url = await prepareLoginUrl('discord', this.redirectUri, baseURL);
    const popup = window.open(
      `${url}&caller=${window.location.origin}`,
      'popup',
      `toolbar=no, location=no, directories=no, status=no, menubar=no, scrollbars=no, copyhistory=no, width=${width}, height=${height}, top=${top}, left=${left}`
    );

    if (!popup) {
      throw new UnknownError({}, 'Failed to open popup window');
    }

    return new Promise((resolve, reject) => {
      // window does not have a closed event, so we need to poll using a timer
      const interval = setInterval(() => {
        if (popup.closed) {
          clearInterval(interval);
          reject(new Error('User closed popup window'));
        }
      }, 1000);

      window.addEventListener('message', (event) => {
        if (event.origin !== (baseURL || LIT_LOGIN_GATEWAY)) {
          return;
        }

        const { provider, token, error } = event.data;

        if (error) {
          clearInterval(interval);
          reject(new Error(error));
        }

        if (provider === 'discord' && token) {
          clearInterval(interval);
          popup.close();
          resolve({
            authMethodType: AUTH_METHOD_TYPE.Discord,
            accessToken: token,
          });
        }
      });
    });
  }

  /**
   * Get auth method id that can be used to look up and interact with
   * PKPs associated with the given auth method
   *
   * @param {AuthMethod} authMethod - Auth method object
   *
   * @returns {Promise<string>} - Auth method id
   */
  public async getAuthMethodId(authMethod: AuthMethod): Promise<string> {
    const userId = await this._fetchDiscordUser(authMethod.accessToken);
    const authMethodId = ethers.utils.keccak256(
      ethers.utils.toUtf8Bytes(`${userId}:${this.clientId}`)
    );
    return authMethodId;
  }

  public static async authMethodId(
    authMethod: AuthMethod,
    clientId?: string
  ): Promise<string> {
    const _clientId = clientId || '1052874239658692668';

    // -- get user id from access token
    let userId;
    const meResponse = await fetch('https://discord.com/api/users/@me', {
      method: 'GET',
      headers: {
        authorization: `Bearer ${authMethod.accessToken}`,
      },
    });
    if (meResponse.ok) {
      const user = await meResponse.json();
      userId = user.id;
    } else {
      throw new UnknownError({}, 'Unable to verify Discord account');
    }

    // -- get auth method id
    const authMethodId = ethers.utils.keccak256(
      ethers.utils.toUtf8Bytes(`${userId}:${_clientId}`)
    );

    return authMethodId;
  }

  /**
   * Fetch Discord user ID
   *
   * @param {string} accessToken - Discord access token
   *
   * @returns {Promise<string>} - Discord user ID
   */
  private async _fetchDiscordUser(accessToken: string): Promise<string> {
    const meResponse = await fetch('https://discord.com/api/users/@me', {
      method: 'GET',
      headers: {
        authorization: `Bearer ${accessToken}`,
      },
    });
    if (meResponse.ok) {
      const user = await meResponse.json();
      return user.id;
    } else {
      throw new UnknownError({}, 'Unable to verify Discord account');
    }
  }
}
