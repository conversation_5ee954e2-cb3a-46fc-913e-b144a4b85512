{"name": "@lit-protocol/lit-auth-client", "version": "7.1.1", "type": "commonjs", "license": "MIT", "homepage": "https://github.com/Lit-Protocol/js-sdk", "repository": {"type": "git", "url": "https://github.com/LIT-Protocol/js-sdk"}, "keywords": ["library"], "bugs": {"url": "https://github.com/LIT-Protocol/js-sdk/issues"}, "publishConfig": {"access": "public", "directory": "../../dist/packages/lit-auth-client"}, "browser": {"crypto": false, "stream": false}, "tags": ["vanilla"], "peerDependencies": {"@simplewebauthn/browser": "^7.2.0", "@simplewebauthn/typescript-types": "^7.0.0"}, "main": "./dist/src/index.js", "typings": "./dist/src/index.d.ts"}