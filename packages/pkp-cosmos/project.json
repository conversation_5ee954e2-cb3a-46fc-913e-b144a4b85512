{"name": "pkp-cosmos", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "packages/pkp-cosmos/src", "projectType": "library", "targets": {"build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/packages/pkp-cosmos", "main": "packages/pkp-cosmos/src/index.ts", "tsConfig": "packages/pkp-cosmos/tsconfig.lib.json", "assets": ["packages/pkp-cosmos/*.md"], "updateBuildableProjectDepsInPackageJson": true}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["packages/pkp-cosmos/**/*.ts"]}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/pkp-cosmos"], "options": {"jestConfig": "packages/pkp-cosmos/jest.config.ts", "passWithNoTests": true}}, "test:watch": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/packages/pkp-cosmos"], "options": {"watch": true, "jestConfig": "packages/pkp-cosmos/jest.config.ts", "passWithNoTests": true}}}, "tags": []}